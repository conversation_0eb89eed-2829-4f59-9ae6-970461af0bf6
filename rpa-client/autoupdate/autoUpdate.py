import json
import os
import sys

import os
import shutil
import socket
import getpass
import win32api
import win32gui
import time
from datetime import time as time1
from tqdm import tqdm
import psutil
from pywinauto.findwindows import find_elements
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from datetime import datetime
import requests
from io import BytesIO
import re


def is_between_midnight_and_5am():
    # 获取当前时间
    now = datetime.now()
    # 创建一个表示凌晨十二点的 time 对象
    midnight = time1(0, 0, 0)
    # 创建一个表示凌晨六点的 time 对象
    six_am = time1(6, 0, 0)
    # 将当前时间的日期部分与午夜的时间部分组合，得到今天的午夜时间
    today_midnight = datetime.combine(now.date(), midnight)
    # 将当前时间的日期部分与凌晨五点的时间部分组合，得到今天的凌晨六点时间
    today_six_am = datetime.combine(now.date(), six_am)
    # todo 判断当前时间是否在午夜和凌晨五点之间
    return True
    # return today_midnight < now < today_six_am


def get_app_title_by_class_name(class_name):
    # 获取企业微信客户端信息
    # class_name_re = 'WeWorkWindow'
    process = None
    element = find_elements(class_name_re=class_name, process=process)
    title = win32gui.GetWindowText(element[0].handle)
    return title

# 填充明文至16字节倍数

def aes_encrypt(data):
    key = b'E13F3D92FC0E09EB2003C4069BF2A778'
    # 使用PKCS5Padding填充消息
    message_padded = pad(data, AES.block_size)
    # 创建AES对象
    aes = AES.new(key, AES.MODE_ECB)
    # 执行加密
    encrypted_message = aes.encrypt(message_padded)
    return base64.b64encode(encrypted_message).decode('utf-8')  # 返回Base64编码后的结果

def download_file(url, data=None, headers=None):
    try:
        ciphertext_base64 = aes_encrypt(json.dumps(data).encode('utf-8'))
        # 发送POST请求
        response = requests.post(url, data=ciphertext_base64, headers=headers, stream=True, verify=False)
        # 检查响应状态码
        if response.status_code == 200:
            # 检查Content-Type是否为文件类型（例如application/octet-stream）
            content_type = response.headers.get('Content-Type')
            if content_type and 'application/octet-stream' in content_type:
                # 文件名
                content_disposition = response.headers.get('Content-Disposition')
                if content_disposition:
                    # 使用正则表达式匹配filename
                    match = re.search(r'filename="?([^";]+)"?', content_disposition)
                    if match:
                        # 返回匹配到的文件名，去掉任何可能的引号
                        file_name = match.group(1)
                    else:
                        raise Exception("filename not found")
                else:
                    raise Exception("invalid download file response")

                # 返回文件流（这里使用BytesIO作为示例，你可以将其保存到文件或其他处理）
                data_chunks = []
                # 获取内容总长度
                total_size = int(response.headers.get('content-length', 0))
                # 初始化进度条
                progress_bar = tqdm(total=total_size, unit='B', unit_scale=True, desc="Downloading")
                # 使用 iter_content 按块下载数据
                for data in response.iter_content(chunk_size=1024):
                    # 写入文件或处理数据
                    # file_stream = BytesIO(b''.join(response.iter_content(chunk_size=1024)))
                    data_chunks.append(data)
                    # file_stream = BytesIO(b''.join(data))
                    # 这里以展示下载进度为主，不做写入操作
                    progress_bar.update(len(data))
                # 关闭进度条
                progress_bar.close()

                file_stream = BytesIO(b''.join(data_chunks))
                file_stream.seek(0)  # 重置流的位置到开头
                return file_stream, file_name
            else:
                # 如果Content-Type不是文件类型，尝试解析为JSON
                try:
                    result = response.json()
                    # 这里可以根据实际情况添加更多的错误处理逻辑
                    # 例如检查result中是否有'message'字段
                    return result, False  # 这里假设即使不是文件，返回JSON也视为失败情况处理
                except ValueError:
                    # 如果无法解析为JSON，返回原始响应内容或抛出异常
                    return {"message": "Failed to parse response as JSON"}, False
        else:
            # 如果状态码不是200，尝试解析为JSON
            try:
                result = response.json()
                return result, False  # 返回包含错误信息的JSON对象
            except ValueError:
                # 如果无法解析为JSON，返回包含默认错误信息的JSON对象
                return {"message": f"Request failed with status code {response.status_code}"}, False
    except requests.RequestException as e:
        # 处理请求异常，例如网络问题、超时等
        return {"message": str(e)}, False
    except Exception as e:
        return {"message": str(e)}, False


def get_pid_by_name(process_name):
    # Iterate over all running processes
    pid_list = []
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            # 支持模糊匹配进程名（包含完整路径的情况）
            if process_name.lower() in proc.info['name'].lower():
                pid_list.append(proc.info['pid'])
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    return pid_list


def kill_process(pid_list):
    """
    Terminate a process by its PID.
    :param pid: The process ID of the process to terminate.
    :return: True if the process was successfully terminated, False otherwise.
    """
    for pid in pid_list:
        try:
            # Get the process object by PID
            process = psutil.Process(pid)
            # Terminate the process
            process.terminate()
            # Optionally, wait for the process to terminate (not strictly necessary)
            process.wait(timeout=3)  # Wait for up to 3 seconds
            return True
        except psutil.NoSuchProcess:
            # The process with the given PID does not exist
            print(f"No process found with PID {pid}")
            return False
        except psutil.AccessDenied:
            # Permission denied to terminate the process
            print(f"Permission denied to terminate process with PID {pid}")
            return False
        except psutil.TimeoutExpired:
            # The process did not terminate within the specified timeout
            print(f"Process with PID {pid} did not terminate within the timeout period")
            # Optionally, you can choose to force kill the process here
            # process.kill()  # Uncomment to force kill the process
    return True


def run_app_by_name(file_path):
    print(f"file path:{file_path}")
    win32api.ShellExecute(0, 'open', file_path, '', '', 1)

    # 获取系统用户名（增强兼容性）
def get_system_user():
    for var in ['LOGNAME', 'USER', 'LNAME', 'USERNAME']:
        user = os.environ.get(var)
        if user:
            print(user)
            return user
    try:
        print(user)
        return getpass.getuser()
    except:
        print("未知用户")
        return "未知用户"
# 获取本地IP地址（局域网）
def get_local_ip():
    try:
        # 创建一个UDP连接（不会实际发送数据）
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))  # 连接Google DNS
            print(s.getsockname()[0])
            return s.getsockname()[0]
    except Exception as e:
        # 回退方案
        hostname = socket.gethostname()
        print(hostname)
        return socket.gethostbyname(hostname)


if __name__ == "__main__":
    while True:
        try:
            if True:
                # 获取企业微信客户端信息
                class_name_re = 'WeWorkWindow'
                title = get_app_title_by_class_name(class_name_re)
                if not title or not title.startswith('wecom' + ' '):
                    print("企业微信和客户端未启动，暂不更新！")
                    time.sleep(1 * 60 * 5)  # 5min
                    continue
                cache = json.loads(title[len('wecom') + 1:])
                version = cache.get('version')
                # todo fix version
                if version is None:
                    version = 0.0
                account = cache.get("account")
                companyAndName = account.split("_")
                company = companyAndName[0]
                pcuser = get_system_user()
                ip = get_local_ip()
                print(version)
                print(company)

                # 尝试下载文件
                print("trying to download...")
                # todo fixurl
                url = "https://dmsc.clic/DMSC/rpa-server-uat/backend/downloadNewClient"
                headers = {"Content-Type": "text/plain"}
                data = {"updateOrg": company, "pcuser": pcuser, "ip": ip} if version is None else {"versionId": version, "updateOrg": company, "pcuser": pcuser, "ip": ip}

                result = download_file(url, data, headers)
                # 检查返回结果
                if isinstance(result[0], BytesIO):
                    # 处理文件流按照文件名保存到当前路径
                    # 1. 创建基础路径
                    base_dir = os.path.join("download", "rpaclient")  # 目标目录
                    file_path = os.path.join(base_dir, "WeComRPA.exe")  # 完整文件路径

                    # 历史归档目录
                    history_base = os.path.join("download", "history")
                    # 创建目标目录（如果不存在）
                    os.makedirs(base_dir, exist_ok=True)

                    # 如果目标文件已存在，先移动到历史目录
                    if os.path.exists(file_path):
                        # 生成时间戳目录名
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        history_dir = os.path.join(history_base, timestamp)

                        # 创建历史目录
                        os.makedirs(history_dir, exist_ok=True)

                        # 移动旧文件到历史目录
                        shutil.move(
                            file_path,
                            os.path.join(history_dir, "WeComRPA.exe")  # 保持文件名一致
                        )
                        print(f"旧文件已归档至：{history_dir}")

                    # 保存新文件到目标路径
                    with open(file_path, "wb") as f:
                        f.write(result[0].getbuffer())
                    print(f"新文件已保存至：{file_path}")

                    # 文件保存成功后 准备打开文件 先关闭原有客户端
                    pid_list = get_pid_by_name("WeComRPA")
                    if pid_list is not None:
                        success = kill_process(pid_list)
                        if success:
                            print(f"WeComRPA Process with PID {pid_list} was successfully terminated")
                        else:
                            print(f"Failed to terminate WeComRPA process with PID {pid_list}... exit!!!")
                            sys.exit()
                    # double check
                    pid_list = get_pid_by_name("WeComRPA")
                    if pid_list is not None:
                        success = kill_process(pid_list)
                        if success:
                            print(f"WeComRPA Process with PID {pid_list} was successfully terminated")
                        else:
                            print(f"Failed to terminate WeComRPA process with PID {pid_list}... exit!!!")
                            sys.exit()
                    # final check
                    pid_list = get_pid_by_name("WeComRPA")
                    if pid_list is not None and len(pid_list) > 0:
                        print(f"Error: can not close WeComRPA process!!{pid_list}")
                        time.sleep(1 * 60 * 60)  # 1hour
                        continue

                    # 启动新的客户端
                    run_app_by_name(file_path)
                    time.sleep(1 * 60 * 5)  # 5min
                else:
                    # 处理JSON对象（例如打印错误信息）
                    print("download Error:", result[0].get("message", "Unknown error"))
                    # time.sleep(1 * 60 * 5)  # 5min
                    time.sleep(1 * 60 * 5)  # 5min
            else:
                time.sleep(1 * 60 * 5)  # 5min
        except Exception as e:
            print("Error:", str(e))
            time.sleep(1 * 60 * 5)  # 5min
