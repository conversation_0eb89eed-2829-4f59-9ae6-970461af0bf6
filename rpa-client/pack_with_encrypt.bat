@echo off
REM Disable command echo

echo Updating version from config.yml...
python update_version.py

REM Generate obfuscated code
pyarmor gen -O obfuscated ./main.py ./config.py ./scheduler.py ./windows_setting.py ./test_wecom.py ./__init__.py


REM Obfuscate subdirectories recursively
pyarmor gen -O obfuscated/ -r ./apps
pyarmor gen -O obfuscated/ -r ./utils
pyarmor gen -O obfuscated/ -r ./autoupdate
pyarmor gen -O obfuscated/ -r ./handler
pyarmor gen -O obfuscated/ -r ./tests
pyarmor gen -O obfuscated/ -r ./assets

REM Copy config file to obfuscated directory
copy .\config.yml obfuscated\config.yml
xcopy .\google obfuscated\google /E /I
xcopy .\airtest obfuscated\airtest /E /I
xcopy .\interception obfuscated\interception /E /I

REM PyInstaller command to create a standalone executable
pyinstaller ^
    --paths=./obfuscated/ ^
    --noconfirm ^
    --onefile ^
    --console ^
    --name "WeComRPA" ^
    --icon "./RPA.ico" ^
    --version-file "./version.py" ^
    --hidden-import "yaml" ^
    --hidden-import "distutils" ^
    --hidden-import "distutils.version" ^
    --hidden-import "cv2" ^
    --hidden-import "json" ^
    --hidden-import "websocket" ^
    --hidden-import "gmssl" ^
    --hidden-import "gmssl.sm4" ^
    --hidden-import "pyperclip" ^
    --hidden-import "win32api" ^
    --hidden-import "win32clipboard" ^
    --hidden-import "win32con" ^
    --hidden-import "win32gui" ^
    --hidden-import "win32process" ^
    --hidden-import "PIL" ^
    --hidden-import "pywinauto" ^
    --hidden-import "interception" ^
    --hidden-import "mss" ^
    --hidden-import "numpy" ^
    --hidden-import "six" ^
    --hidden-import "uuid" ^
    --hidden-import "comtypes.stream" ^
    --hidden-import "logging.handlers" ^
    --collect-all "pyzbar" ^
    --add-data "./obfuscated/config.py;." ^
    --add-data "./obfuscated/airtest;airtest/" ^
    --add-data "./obfuscated/interception;interception/" ^
    --add-data "./obfuscated/utils;utils/" ^
    --add-data "./obfuscated/google;google/" ^
    --add-data "./obfuscated/config.yml;." ^
    --add-data "./obfuscated/scheduler.py;." ^
    --add-data "./obfuscated/windows_setting.py;." ^
    --add-data "./SetDpi.exe;." ^
    --add-data "./obfuscated/apps;apps/" ^
    --add-data "./obfuscated/handler;handler/" ^
    --add-data "./assets;assets/" ^
    --add-data "./config.yml;." ^
    --add-data "./obfuscated/test_wecom.py;." ^
    "./obfuscated/main.py"

REM Check for errors
IF %ERRORLEVEL% NEQ 0 (
    echo "PyInstaller failed with error code %ERRORLEVEL%"
    pause
    exit /b %ERRORLEVEL%
)

REM Delete the build and obfuscated directory if build succeeds
rmdir /s /q build
rmdir /s /q obfuscated

echo "Build completed successfully"
pause
exit /b 0
