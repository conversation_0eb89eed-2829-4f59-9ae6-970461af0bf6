# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(1, 2, 1, 0),
    prodvers=(1, 2, 1, 0),
    # Set mask to FILEFLAGSMASK
    mask=0x3f,
    # Contains a bitmask that specifies the valid bits 'flags'r
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x4,
    # The general type of file.
    # 0x1 - app
    fileType=0x1,
    # The function of the file.
    # 0x0 - unknown
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'000004b0', 
        [StringStruct(u'Comments', u'RPA for WeCom'),
        StringStruct(u'CompanyName', u'My Company'),
        StringStruct(u'FileDescription', u'WeCom RPA'),
        StringStruct(u'FileVersion', u'1.2.1'),
        StringStruct(u'InternalName', u'WeComRPA'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024 My Company.'),
        StringStruct(u'OriginalFilename', u'WeComRPA.exe'),
        StringStruct(u'ProductName', u'WeCom RPA'),
        StringStruct(u'ProductVersion', u'1.2.1')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [0, 1200])])
  ]
) 