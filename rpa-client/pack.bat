@echo off
REM Disable command echo

echo Updating version from config.yml...
python update_version.py

REM PyInstaller command to create a standalone executable
pyinstaller ^
    --noconfirm ^
    --onefile ^
    --console ^
    --name "WeComRPA" ^
    --icon "./RPA.ico" ^
    --version-file "./version.py" ^
    --hidden-import "yaml" ^
    --hidden-import "distutils" ^
    --hidden-import "distutils.version" ^
    --hidden-import "cv2" ^
    --hidden-import "json" ^
    --hidden-import "websocket" ^
    --hidden-import "gmssl" ^
    --hidden-import "gmssl.sm4" ^
    --hidden-import "pyperclip" ^
    --hidden-import "win32api" ^
    --hidden-import "win32clipboard" ^
    --hidden-import "win32con" ^
    --hidden-import "win32gui" ^
    --hidden-import "interception" ^
    --hidden-import "win32process" ^
    --hidden-import "PIL" ^
    --hidden-import "pywinauto" ^
    --hidden-import "mss" ^
    --hidden-import "numpy" ^
    --hidden-import "six" ^
    --hidden-import "uuid" ^
    --hidden-import "comtypes.stream" ^
    --hidden-import "logging.handlers" ^
    --collect-all "pyzbar" ^
    --add-data "./config.py;." ^
    --add-data "./airtest;airtest/" ^
    --add-data "./interception;interception/" ^
    --add-data "./google;google/" ^
    --add-data "./config.yml;." ^
    --add-data "./scheduler.py;." ^
    --add-data "./windows_setting.py;." ^
    --add-data "./SetDpi.exe;." ^
    --add-data "./apps;apps/" ^
    --add-data "./handler;handler/" ^
    --add-data "./assets;assets/" ^
    --add-data "./utils;utils/" ^
    --add-data "./test_wecom.py;." ^
    "./main.py"

REM Check for errors
IF %ERRORLEVEL% NEQ 0 (
    echo "PyInstaller failed with error code %ERRORLEVEL%"
    pause
    exit /b %ERRORLEVEL%
)

REM Delete the obfuscated directory if build succeeds
rmdir /s /q build

echo "Build completed successfully"
pause
