import yaml
import re
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def update_version_py():
    """
    Reads the version from config.yml and updates version.py accordingly.
    This script should be run from the project root directory.
    """
    try:
        # Define file paths
        config_path = os.path.join('', 'config.yml')
        version_py_path = os.path.join('', 'version.py')

        # 1. Read version from config.yml
        logging.info(f"Reading version from '{config_path}'...")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        rpa_version_str = config.get('rpaversion')
        if not rpa_version_str:
            logging.error(f"'rpaversion' not found in '{config_path}'. Aborting.")
            return

        logging.info(f"Found version in config.yml: {rpa_version_str}")

        # 2. Parse version string
        parts = rpa_version_str.split('.')
        if len(parts) != 3:
            logging.error(f"Invalid version format '{rpa_version_str}'. Expected 'x.y.z'. Aborting.")
            return
            
        major, minor, patch = parts
        version_tuple_str = f"({major}, {minor}, {patch}, 0)"

        # 3. Read version.py content
        logging.info(f"Reading content from '{version_py_path}'...")
        with open(version_py_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 4. Replace version numbers using regex
        logging.info("Replacing version numbers...")
        # Use \g<1> to avoid ambiguity with version numbers in the replacement string.
        # Made the version match more flexible with [\d\.]*
        content_new, count1 = re.subn(r"filevers=\s*\(\s*\d+,\s*\d+,\s*\d+,\s*\d+\s*\)", f"filevers={version_tuple_str}", content)
        content_new, count2 = re.subn(r"prodvers=\s*\(\s*\d+,\s*\d+,\s*\d+,\s*\d+\s*\)", f"prodvers={version_tuple_str}", content_new)
        content_new, count3 = re.subn(r"(StringStruct\(\s*u'FileVersion',\s*u')[\d\.]*(')", f"\\g<1>{rpa_version_str}\\g<2>", content_new)
        content_new, count4 = re.subn(r"(StringStruct\(\s*u'ProductVersion',\s*u')[\d\.]*(')", f"\\g<1>{rpa_version_str}\\g<2>", content_new)

        total_changes = count1 + count2 + count3 + count4
        if total_changes < 4:
            logging.warning("Some version strings were not found or replaced. Please check 'version.py'.")
        
        if content == content_new:
            logging.info(f"'{version_py_path}' is already up to date with version {rpa_version_str}. No changes made.")
        else:
            # 5. Write updated content back to version.py
            logging.info(f"Writing updated content to '{version_py_path}'...")
            with open(version_py_path, 'w', encoding='utf-8') as f:
                f.write(content_new)
            logging.info(f"Successfully updated '{version_py_path}' to version {rpa_version_str}")

    except FileNotFoundError as e:
        logging.error(f"Error: {e}. Make sure you are running this script from the project's root directory.")
    except Exception as e:
        logging.error(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    update_version_py() 