import logging
import signal
import sys
import time
import winreg
import os
import unittest
from windows_setting import *
from config import SERVER_HOST, SERVER_PORT, SERVER_PATH, SERVER_SSL, APP_SIZE, APP_PATHS
from scheduler import Scheduler
from test_wecom import suite as wecom_test_suite # Import the test suite function

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from airtest.core.api import *

def set_autostart(exe_path, entry_name, delayed_arg="--delayed-start"):
    """
    Sets the application to start automatically on user login via Windows Registry.
    Includes a delayed start argument.
    """
    try:
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                             r"Software\Microsoft\Windows\CurrentVersion\Run",
                             0, winreg.KEY_SET_VALUE)
        # Ensure the path is quoted in case it contains spaces
        command = f'"{exe_path}" {delayed_arg}'
        winreg.SetValueEx(key, entry_name, 0, winreg.REG_SZ, command)
        winreg.CloseKey(key)
        logging.info(f"Successfully set autostart for '{entry_name}' to '{command}'")
    except Exception as e:
        logging.error(f"Failed to set autostart: {e}")

if __name__ == '__main__':
    # Set autostart entry when the program runs
    # Use sys.argv[0] to get the path of the currently executing file (handles .exe after packaging)
    exe_path = os.path.abspath(sys.argv[0])
    entry_name = "WeComRPA" # Use the specified program name
    set_autostart(exe_path, entry_name)

    # Check for the delayed start argument and wait if present
    if "--delayed-start" in sys.argv:
        logging.info("Delayed start detected. Waiting for 30 seconds...")
        time.sleep(30)

    # Check for the debug/test mode argument
    if "--run-tests" in sys.argv:
        logging.info("Debug mode: Running WecomTestSuite regression tests...")
        runner = unittest.TextTestRunner()
        runner.run(wecom_test_suite())
        sys.exit(0) # Exit after running tests

    # Normal operation mode
    signal.signal(signal.SIGINT, lambda *args: sys.exit())
    signal.signal(signal.SIGTERM, lambda *args: sys.exit())
    prevent_sleep()
    # set_dpi(100)
    while True:
        scheduler = Scheduler(host=SERVER_HOST, port=SERVER_PORT, path=SERVER_PATH, ssl=SERVER_SSL, app_size=APP_SIZE, app_paths=APP_PATHS)
        scheduler.startup()
        logging.error('Unable to connect to the server host: %s, port: %d, path: %s, ssl: %s', SERVER_HOST, SERVER_PORT, SERVER_PATH, SERVER_SSL)
        scheduler.shutdown()
        time.sleep(5)
