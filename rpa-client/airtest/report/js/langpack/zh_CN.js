Lang.prototype.pack.zh = {
    "token": {
        "Airtest Report": "Airtest 报告",
        "script running report": "脚本运行报告",
        "Contact Us": "联系我们",
        "I am sorry, this log file is empty!": "你没有眼花，这个Log文件就是一片空白",
        "Test Case": "测试用例",
        "Steps: ": "步骤数：",
        "Time: ": "耗时",
        "Quick view": "快览",
        "[Passed]": "[运行成功]",
        "[Failed]": "[运行失败]",
        '[Terminated]': "[任务超时]",
        "Executors": "执行者",
        "Author:": "作者：",
        "Author: Anonymous": "作者：佚名",
        "Device:": "设备:",
        "Connect:": '连接:',
        "Accomplished:": "已完成:",
        "Rate:": '成功率:',
        "Succeed:": '成功运行:',
        'Failed:': '运行失败:',
        "No of device:": '设备序号:',
        "No of script:": "脚本序号:",
        "Type:": '运行类型:',
        "Assert: Test Steps Summary": "Assert 测试点汇总",
        "Test step": "测试点",
        "Steps:": "步骤数：",
        "Expected result:": "预期结果：",
        "order": "顺序",
        "duration": "耗时",
        "status": "状态",
        "Jump to wrong step": "跳至错误步骤",
        "Filter by:": "筛选:",
        "All": "全部",
        "Failed": "失败",
        "Success": "成功",
        "Assert": "断言",
        "show all steps": "显示所有步骤",
        "show success steps only": "只显示成功步骤",
        "show failed steps only": "只显示失败步骤",
        "show steps with assertion only": "只显示断言",
        "No record information found.": "暂无设备录屏信息",
        "Target object": "目标图片",
        "Runtime screenshot": "运行时屏幕截图",
        "Confidence:": "匹配度：",
        "Execution finished successfully": "执行成功",
        "Execution failed": "执行失败",
        "Steps navigation": "步骤导航",
        "Related information": "相关信息",
        "Touch": "点击",
        "Swipe": "滑动",
        "Wait": "等待目标出现",
        "Exists": "根据图片是否存在选择分支",
        "Text": "输入",
        "Keyevent": "按键",
        "Sleep": "等待",
        "Server call": "调用服务器方法",
        "Assert exists": "断言目标存在",
        "Assert not exists": "断言目标不存在",
        "Snapshot": "截图",
        "Assert equal": "断言相等",
        "Assert not equal": "断言不相等",
        "Assert:": "断言：",
        "Title": "标题",
        "Description:": "描述：",
        "Time:": "时间：",
        "Behavior:": "动作：",
        "Start:": "时间：",
        "Duration:": "耗时：",
        "Status:": "结果：",
        "Total": "共",
        "Warning: No steps": "警告：没有步骤",
        "© 1997 - 2019 NetEase, Inc. All Rights Reserved.": "© 1997 - 2019 网易公司版权所有"
    }
};