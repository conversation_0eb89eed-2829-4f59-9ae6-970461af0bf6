@charset "utf-8";
* {
    padding: 0;
    margin: 0;
}

html {
    height: 100%;
}

body {
    background: linear-gradient(#0A0011, #0C0B1F, #101628, #151D2F, #11182A, #150D21, #0A0011);
    color: rgb(237, 237, 237);
    font-size: 14px;
    height: 100%;
    position: relative;
}

.container-fluid {
    margin: auto;
    padding-right: 100px;
    padding-left: 100px;
    margin-bottom: 220px;
    min-width: 960px;
    min-height: calc(100% - 222px);
    position: relative;
}

#back_multi .back {
    position: fixed;
    left: 20px;
    top: 50px;
    width: 50px;
    height: 50px;
    display: block;
    border: solid 1px #2f4682;
    border-radius: 40px;
}

#back_multi .back:hover {
    background: #223159;
}

#back_multi .back img {
    width: 25px;
    margin-top: 12px;
    margin-left: 12px;
}

h2.empty-report {
    text-align: center;
    margin-bottom: 60px;
    margin-top: 30px;
}

.title {
    font-size: 32px;
    text-align: center;
    padding-top: 50px;
}

.info-title {
    font-size: 20px;
    margin-bottom: 20px
}

.summary {
    margin: 30px 0;
    background: #121c34;
    padding-top: 30px;
    padding-left: 30px;
}

.summary .show-vertical, .summary .info3, .summary .show-horizontal .info {
    width: 48%;
    display: inline-block
}

.summary .show-horizontal .info2 {
    /* border-left: dashed 1px rgb(102,102,102); */
}

.summary .info {
    line-height: 18px;
}

.summary .info3 {
    /* border-left: dashed 1px rgb(102,102,102); */
    padding-left: 50px;
    min-height: 260px;
    vertical-align: top;
    width: calc(48% - 50px);
}

.summary .info-left, .summary .info-right {
    display: inline-block;
}

.summary .info-left {
    width: 60px;
    height: 100%;
    vertical-align: top;
}

.summary .info-right {
    width: calc(100% - 80px);
}

.summary .info-title .green, .summary .info-title .red {
    color: rgb(184, 233, 134);
    font-size: 16px;
    margin-left: 48px;
    display: inline-block;
}

.summary .info-title .red {
    color: #fd5a3e;
}

.summary .info-content .info-sub {
    display: inline-block;
    margin-right: 30px;
}

.summary .info-toal {
    margin: 18px 0 35px 0
}

.summary .info-toal > div {
    display: inline-block;
    height: 30px;
    min-width: 30%;
}

.summary .info-toal .info-step, .summary .info-toal .info-console {
    margin-right: 100px;
}

.summary .info-toal .info-console img#show-console {
    width: 25px;
    margin-left: 10px;
    vertical-align: middle;
}

.summary .info-toal .info-console img#show-console:hover {
    content: url(../image/console_hover.svg);
}

.summary .info-toal .info-value {
    color: rgb(74, 144, 226);
    font-size: 18px;
    padding-left: 5px;
}

.summary .info-toal .info-value a {
    color: rgb(74, 144, 226);
}

.summary .info-toal .info-value img {
    margin-left: 10px;
    width: 15px;
}

.summary .info-execute {
    line-height: 18px;
    margin: 20px 0;
    cursor: context-menu;
    position: relative;
}

.summary .info-execute #copy_path {
    display: inline-block;
    margin-left: 10px;
    width: 20px;
    vertical-align: bottom;
    cursor: context-menu;
    opacity: 0.6;
}

.summary .info-execute #copy_path:hover {
    opacity: 1;
}

.summary .circle-img {
    background: rgb(216, 216, 216);
    width: 18px;
    height: 18px;
    float: left;
    border-radius: 10px;
    margin-right: 10px;
}

.summary .info-execute .info-name {
    line-height: 18px;
}

.summary .airdesc.long .show-more {
    content: '';
    height: 25px;
    width: 25px;
    display: block;
    background: url(../image/less.svg) no-repeat;
    background-size: contain;
    margin-left: 50px;
    opacity: 0.5;
    position: absolute;
}

.summary .airdesc.collapse .show-more {
    display: block;
    transform: rotate(180deg);
}

.summary .airdesc {
    padding-bottom: 30px;
    position: relative;
}

.summary .airdesc .desc-content {
    height: auto;
    overflow-y: hidden;
    line-height: 24px;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-word;
}

.summary .airdesc.collapse .desc-content {
    height: 160px;
}

.gallery {
    margin-bottom: 30px;
}

.gallery .info-title {
    margin-bottom: 10px;
}

.gallery .content {
    background: rgb(18, 28, 52);
    overflow-x: scroll;
    white-space: nowrap;
    background: #121c34;
    padding-top: 20px;
}

.gallery .thumbnail {
    display: inline-block;
    border: solid 1px transparent;
}

.gallery .thumbnail.active {
    border: solid 1px rgb(74, 144, 226);
}

.gallery .thumbnail img {
    max-width: 120px;
    max-height: 130px;
}

.gallery .thumbnail .time {
    text-align: center;
    font-size: 12px;
    margin: 3px 0;
}

.step-list .no-steps {
    text-align: center;
}

.step-list .content {
    background: rgb(18, 28, 52)
}

#device {
    display: none;
    flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    padding: 40px 0 30px 80px;
    background: #121c34;
    margin-bottom: 49px;

}

#device.show {
    display: -ms-flexbox;
    display: flex;
    justify-content: flex-start;
}

#device .info {
    width: 48%;
    line-height: 30px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#device .info.connect {
    position: relative;
    padding-right: 30px;
}

#device .info .copy_device {
    content: "";
    width: 18px;
    height: 18px;
    background: url(../image/copy.svg) no-repeat;
    background-size: contain;
    position: absolute;
    right: 4px;
    top: 3px;
    opacity: 0.6;
}

#device .info .copy_device:hover {
    opacity: 1;
}

#device .info span:first-child {
    margin-right: 10px;
}


.steps-head {
    position: relative;
    background: rgb(22, 34, 62);
    line-height: 40px;
}

.head-left {
    padding-left: 20px;
}

.steps-head .order {
    width: 100px;
    text-align: center;
    display: inline-block;
    cursor: context-menu;
    position: relative;
}

.steps-head .order::after {
    content: "";
    width: 16px;
    height: 16px;
    display: inline-block;
    margin-left: 7px;
    vertical-align: middle;
}

.steps-head .order:hover::after {
    background: url(../image/order.svg);
}

.head-right {
    position: absolute;
    top: 0;
    right: 0;
}

.head-right .filters {
    margin-right: 20px;
    margin-left: 10px;
}

.head-right .filter {
    margin: 0 3px;
}

.head-right .jump-wrong {
    margin-right: 40px;
}

.head-right .jump-wrong, .head-right .filter {
    padding: 0 10px;
    background: #1F3057;
    cursor: context-menu;
}

.head-right .jump-wrong:hover, .head-right .filter:hover {
    background: #283D6F
}

.head-right .filter.active {
    background: #8B632A;
}

.steps-content {
    background: rgb(18, 28, 52);
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: -moz-box;
    display: -moz-flex;
    display: flex;
    -webkit-box-align: stretch;
    -webkit-align-items: stretch;
    -moz-box-align: stretch;
    -moz-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
}

.step-left {
    min-width: 320px;
    padding: 30px 50px 30px 20px;
    width: 32%;
    max-width: 500px;
    max-height: 900px;
    overflow-y: auto;
}

.step-right {
    border-left: dashed 1px rgb(102, 102, 102);
    padding: 30px 0px 0 60px;
    vertical-align: top;
    min-height: 500px;
    width: 100%;
    max-width: calc(100% - 410px);
    max-width: calc(100% - 600px);
}

.step-left .step {
    line-height: 36px;
    position: relative;
    cursor: context-menu;
    padding: 0 10px;
}

.step-left .step:hover {
    background: rgb(22, 34, 62)
}

.step-left .step.active {
    background: #040F26
}

.step-left .step > img {
    vertical-align: middle;
}

.step-left .step .step-context {
    position: absolute;
    width: 20px;
    top: 10px;
    right: -25px;
    cursor: context-menu;
    opacity: 0.6;
}

.step-left .step .step-context:hover {
    opacity: 1;
}

.step-left .order {
    margin: 0 5px;
}

.step-left .step_title {
    word-break: break-all;
}

.step-left .step-time {
    position: absolute;
    right: 10px;
}

.step-right .step-head {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    word-break: break-all;
}

.step-right .step-status {
    margin-right: 20px;
    border-radius: 5px;
    font-size: 18px;
    font-weight: bolder;
    padding: 3px 7px;
}

.step-right .step-status.success {
    background: #417505;
}

.step-right .step-status.fail {
    background: #fd5a3e
}

.step-right .step-infos {
    line-height: 30px;
}

.step-right .step-infos .content-val.success {
    color: #97cc64;
}

.step-right .step-infos .content-val.fail {
    color: #fd5a3e;
}

.step-right .step-infos img {
    vertical-align: middle;
    margin: 0 5px;
}

.step-right .bold {
    font-size: 18px;
    font-weight: bold;
}

.step-right .step-args {
    width: 100%;
    border-top: solid 1px #212f53;
    margin-top: 20px;
    padding-top: 30px;
    padding-bottom: 50px;
}

.step-right .step-args .crop_image {
    margin-top: 7px;
    max-width: calc(100% - 80px);
    max-height: 100%;
}

.step-right .step-args .desc {
    margin-left: 50px;
}

.step-right .fancybox {
    margin: auto;
    position: relative;
    max-width: 100%;
}

.step-right .step-args .fluid {
    display: inline-block;
    vertical-align: top;
    margin-bottom: 20px;
}

.fluid.infos {
    min-width: 300px;
}

.step-right .fluid.screens, .step-right .fluid.traces {
    width: 100%;
    max-width: calc(100% - 330px);
    min-width: 500px;
}

.fancybox .screen {
    max-width: 100%;
    border: solid 1px #212f53;
    max-height: 600px;
}

.fancybox .target {
    position: absolute;
    width: 50px;
    height: 50px;
    animation-name: rubberBand;
    -webkit-animation-name: rubberBand; /* Safari 和 Chrome */
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

.fancybox .rect {
    position: absolute;
    border: solid 2px red;
    border-radius: 3px;
    background: transparent;
}

.fancybox .arrow {
    display: flex;
    align-items: center;
    left: 100px;
    top: 100px;
    position: absolute;
    width: 120px;
    margin-top: -15px;
    margin-left: -6px;
}

.fancybox .arrow .start {
    width: 12px;
    height: 12px;
    border-radius: 6px;
    background-color: red;
}

.fancybox .arrow .line {
    flex: 1;
    background: red;
    height: 10px;
}

.fancybox .arrow .end {
    width: 0;
    height: 0;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 20px solid red;
}

pre.trace {
    white-space: pre-wrap;
    word-break: break-word;
}

#pageTool {
    margin-top: 50px;
    position: relative;
    height: 50px;
}

#pageTool .js-page-first, #pageTool .js-page-last {
    display: none;
}

#pageTool ul {
    padding-left: 0;
}

#pageTool .ui-paging-container li {
    margin-bottom: 10px;
}

#pageTool .ui-pager-disabled {
    color: rgb(59, 81, 134);
    border: solid 1px #233868;
    background: transparent;
}

#pageTool .focus, #pageTool li.ui-pager:hover {
    color: rgb(74, 144, 226);
    background: transparent;
    border: solid 1px rgb(74, 144, 226);
}

#pageTool .steps-total {
    display: block;
    margin: auto;
    text-align: center;
    color: rgb(74, 144, 226);
    font-size: 12px;
    position: absolute;
    top: 4px;
    right: -14px;
}

#pageTool .ui-paging-container {
    position: absolute;
    right: 50px;
    top: 0px;
}

#pageTool .ui-select-pagesize, #pageTool .ui-paging-count {
    background: transparent;
    color: rgb(74, 144, 226);
}

.row.gif-wrap {
    position: fixed;
    bottom: 0;
    right: 0;
}

.row.gif-wrap .menu {
    display: block;
    text-align: right;
    background: rgba(0, 0, 0, 0.7);
    min-width: 100px;
    height: 38px;
}

.row.gif-wrap .pattern1, .row.gif-wrap .col-md-6 {
    display: none;
}

.row.gif-wrap.show .pattern1, .row.gif-wrap.show .col-md-6 {
    display: block;
}

.row.gif-wrap .pattern2 {
    display: block;
}

.row.gif-wrap.show .pattern2 {
    display: none;
}

.row.gif-wrap .pattern {
    margin-right: 14px;
}

.row.gif-wrap .pattern > div {
    display: inline-block;
    padding: 8px;
    width: 20px;
    height: 20px;
    margin-left: 2px;
}

.row.gif-wrap .pattern .minimize {
    width: 18px;
}

.row.gif-wrap .embed-responsive {
    display: inline-block;
    position: relative;
}

.row.gif-wrap .embed-responsive .open_in_new_tab {
    position: absolute;
    top: -34px;
    right: 100px;
    transition: all 0.3s ease-out;
    background: rgba(0, 0, 0, 0.7);
    padding: 8px;
    border-radius: 44px;
    width: 16px;
    height: 15px;
    z-index: 2;
}

.row.gif-wrap img {
    max-width: 100%;
    max-height: 100%;
    opacity: 0.6;
}

.row.gif-wrap img:hover {
    opacity: 1;
    transform: scale(1.1);
}

.row.gif-wrap .embed-responsive-item {
    max-width: 500px;
    max-height: 500px;
}

.mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    z-index: 2;
    display: none;
}

.mask .content {
    width: 80%;
    height: calc(100% - 140px);
    margin: auto;
    margin-top: 60px;
    padding-top: 40px;
    position: relative;
}

.mask .content .console-content {
    height: 100%;
    overflow-y: auto;
}

#magnify.mask {
    background: rgba(255, 255, 255, 0.7);
}

#magnify .content {
    padding: 0;
    height: calc(100% - 100px);
    display: flex;
    justify-content: center;
    align-items: center;
}

#magnify .fancybox, #magnify .crop_image {
    display: block;
    max-width: 100%;
    max-height: 100%;
    position: relative;
}

#magnify .fancybox .screen {
    height: auto;
    max-height: 100%;
    margin: auto;
    display: block;
}

#close-console {
    position: absolute;
    top: 7px;
    right: 10px;
    width: 25px;
    border: solid 1px white;
    border-radius: 30px;
}

#close-console:hover {
    background: rgba(255, 255, 255, 0.3);
}

.footer {
    height: 120px;
    color: #999;
    min-width: 1160px;
    border-top: solid 1px #172036;
    position: absolute;
    width: 100%;
    bottom: -219px;
    left: 0;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 34px 0;
}

.foo {
    min-width: 300px;
    width: 20%;
}

.foo:last-child {
    min-width: 455px;
    font-size: 14px;
}

.footer a {
    text-decoration: none;
    display: block;
    color: inherit;
}

.footer .foo > div {
    display: inline-block;
    vertical-align: middle;
}

.footer .interfaces .icon:first-child {
    border: none;
}

.footer .interfaces .icon {
    margin: 15px 0;
    display: inline-block;
    padding: 0 16px;
    border-left: solid 1px #666666;
}

.footer .icon img {
    vertical-align: middle;
}

.footer .apps .icon {
    display: inline-block;
    line-height: 40px;
    margin-right: 30px;
}

.footer .corp img {
    width: 105px;
    vertical-align: middle;
    margin-right: 20px;
}


/* animation */
@-webkit-keyframes rubberBand {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
    30% {
        -webkit-transform: scale3d(1.25, 0.75, 1);
        transform: scale3d(1.25, 0.75, 1);
    }
    40% {
        -webkit-transform: scale3d(0.75, 1.25, 1);
        transform: scale3d(0.75, 1.25, 1);
    }
    50% {
        -webkit-transform: scale3d(1.15, 0.85, 1);
        transform: scale3d(1.15, 0.85, 1);
    }
    65% {
        -webkit-transform: scale3d(.95, 1.05, 1);
        transform: scale3d(.95, 1.05, 1);
    }
    75% {
        -webkit-transform: scale3d(1.05, .95, 1);
        transform: scale3d(1.05, .95, 1);
    }
    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
}


@keyframes down {
    0% {
        bottom: 10px;
    }
    50% {
        bottom: 3px;
    }
    100% {
        bottom: 10px;
    }
}

@-moz-keyframes down /* Firefox */
{
    0% {
        bottom: 10px;
    }
    50% {
        bottom: 3px;
    }
    100% {
        bottom: 10px;
    }
}

@-webkit-keyframes down /* Safari 和 Chrome */
{
    0% {
        bottom: 10px;
    }
    50% {
        bottom: 3px;
    }
    100% {
        bottom: 10px;
    }
}

@-o-keyframes down /* Opera */
{
    0% {
        bottom: 10px;
    }
    50% {
        bottom: 3px;
    }
    100% {
        bottom: 10px;
    }
}


/* 滚动条 */
::-webkit-scrollbar {
    width: 6px; /*滚动条的宽度*/
    height: 6px; /*滚动条的高度*/
}

::-webkit-scrollbar-thumb:horizontal { /*水平滚动条的样式*/
    background-color: #324163;
    -webkit-border-radius: 6px;
    outline: 2px solid #fff;
}

::-webkit-scrollbar-track-piece {
    background-color: rgb(10, 10, 28); /*滚动条的背景颜色*/
    -webkit-border-radius: 0; /*滚动条的圆角宽度*/
}

::-webkit-scrollbar-thumb:vertical { /*垂直滚动条的样式*/
    height: 50px;
    background-color: #324163;
    -webkit-border-radius: 4px;
    outline: 2px solid #fff;
    outline-offset: -2px;
    border: 1px solid #324163;
}

::-webkit-scrollbar-thumb:hover { /*滚动条的hover样式*/
    height: 50px;
    background-color: #9f9f9f;
    -webkit-border-radius: 4px;
}


/* 分页 */

.ui-paging-container {
    color: #666;
    font-size: 12px
}

.ui-paging-container ul {
    overflow: hidden;
    text-align: center
}

.ui-paging-container li, .ui-paging-container ul {
    list-style: none
}

.ui-paging-container li {
    display: inline-block;
    padding: 3px 6px;
    margin-left: 5px;
    color: #666
}

.ui-paging-container li.ui-pager {
    cursor: pointer;
    border: 1px solid #ddd;
    border-radius: 2px
}

.ui-paging-container li.focus, .ui-paging-container li.ui-pager:hover {
    background-color: #288df0;
    color: #FFF
}

.ui-paging-container li.ui-paging-ellipse {
    border: none
}

.ui-paging-container li.ui-paging-toolbar {
    padding: 0
}

.ui-paging-container li.ui-paging-toolbar select {
    height: 22px;
    border: 1px solid #ddd;
    color: #666
}

.ui-paging-container li.ui-paging-toolbar input {
    vertical-align: top;
    line-height: 20px;
    height: 20px;
    padding: 0;
    border: 1px solid #ddd;
    text-align: center;
    width: 30px;
    margin: 0 0 0 5px
}

.ui-paging-container li.ui-paging-toolbar a {
    text-decoration: none;
    display: inline-block;
    height: 20px;
    border: 1px solid #ddd;
    vertical-align: top;
    border-radius: 2px;
    line-height: 20px;
    padding: 0 3px;
    cursor: pointer;
    margin-left: 5px;
    color: #666
}

.ui-paging-container li.ui-pager-disabled, .ui-paging-container li.ui-pager-disabled:hover {
    background-color: #f6f6f6;
    cursor: default;
    border: none;
    color: #ddd
}