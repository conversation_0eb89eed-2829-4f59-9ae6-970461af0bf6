@echo off
REM Disable command echo

REM PyInstaller command to create a standalone executable
pyinstaller ^
    --noconfirm ^
    --onefile ^
    --console ^
    --name "WAutoUpdate" ^
    --icon "./RPA.ico" ^
    --hidden-import "comtypes.stream" ^
    --hidden-import "win32api" ^
    --hidden-import "win32clipboard" ^
    --hidden-import "win32con" ^
    --hidden-import "win32gui" ^
    --hidden-import "win32process" ^
    --add-data "./autoupdate;autoupdate/" ^
    "./autoupdate/autoUpdate.py"

REM Check for errors
IF %ERRORLEVEL% NEQ 0 (
    echo "PyInstaller failed with error code %ERRORLEVEL%"
    pause
    exit /b %ERRORLEVEL%
)

REM Delete the obfuscated directory if build succeeds
rmdir /s /q build

echo "Build completed successfully"
pause
