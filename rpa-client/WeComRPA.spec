# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all

datas = [('./config.py', '.'), ('./airtest', 'airtest/'), ('./google', 'google/'), ('./config.yml', '.'), ('./scheduler.py', '.'), ('./windows_setting.py', '.'), ('./SetDpi.exe', '.'), ('./apps', 'apps/'), ('./handler', 'handler/'), ('./assets', 'assets/'), ('./utils', 'utils/'), ('./test_wecom.py', '.')]
binaries = []
hiddenimports = ['yaml', 'distutils', 'distutils.version', 'cv2', 'json', 'websocket', 'gmssl', 'gmssl.sm4', 'pyperclip', 'win32api', 'win32clipboard', 'win32con', 'win32gui', 'interception', 'win32process', 'PIL', 'pywinauto', 'mss', 'numpy', 'six', 'uuid', 'comtypes.stream', 'logging.handlers']
tmp_ret = collect_all('pyzbar')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='WeComRPA',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['RPA.ico'],
)
