import ctypes
import os
import sys
from win32 import win32api

# 定义常量
ES_CONTINUOUS = 0x80000000
ES_DISPLAY_REQUIRED = 0x00000002
ES_SYSTEM_REQUIRED = 0x00000001

# 调用 Windows API 关闭息屏
def prevent_sleep():
    ctypes.windll.kernel32.SetThreadExecutionState(ES_CONTINUOUS | ES_DISPLAY_REQUIRED | ES_SYSTEM_REQUIRED)

def set_dpi(scale):
    # 获取当前的可执行文件路径
    if getattr(sys, 'frozen', False):
        # PyInstaller 打包后的情况
        base_path = sys._MEIPASS  # PyInstaller 临时目录
    else:
        # 未打包时，使用当前脚本的目录
        base_path = os.path.dirname(__file__)

    # 拼接 other_program.exe 的路径
    exe_path = os.path.join(base_path, "SetDpi.exe")
    win32api.ShellExecute(0, 'open', exe_path, f' {scale}', '', 1)

