import logging
import time

import config
import unittest
import random
import win32gui

from apps.app import MessageType
from apps.wecom import WeCom

APP = WeCom


class WecomTestSuite(unittest.TestCase):

    def get_client(self) -> WeCom:
        elements = APP.find_elements()
        first = elements[0]
        return APP(first.handle, first.process_id, 0)

    def test_01_launch(self):
        elements = APP.find_elements()
        if not elements:
            APP.launch(1)

    def test_02_login(self):
        client = self.get_client()
        class_name = APP.find_elements()[0].class_name
        if client.is_login_window(class_name):
            qrcode = client.login(None)
            logging.info(qrcode)

    # def test_03_logout(self):
    #     client = self.get_client()
    #     client.logout(None)

    def test_04_reset_title(self):
        client = self.get_client()
        win32gui.SetWindowText(client.handle, '')

    def test_05_init(self):
        client = self.get_client()
        client.init()
        logging.debug(client.user)
        assert client.user is not None

    # def test_06_find_userinfo(self):
    #     client = self.get_client()
    #     user = client.find_userinfo()
    #     logging.debug(user)
    #     assert user is not None


    @APP.print_run_time
    def test_07_send_private_messages(self):
        client = self.get_client()
        client.send_private_messages({
            "target": "测试人员",
            "messages": [
                {"type": MessageType.TEXT, "content": "Hey, dude.🐟#0001139"},
                {"type": MessageType.MINIPROGRAM, "content": '{"appid":"wx4f5dd50093b84881","title":"个人养老金政策解读","imgUrl":"https://dmsc.e-chinalife.com/cms/api-object/object/private/7e1bd9c8bf894a0a731ec49d8b73e08f/DA8CCEFEBA6C8939D5AE0BDACE62730064E7D0CAEABD4AA607F01C9089BE1C2DD5C542FCBC7C1C1110E8B5037EE64D3EF1A415C5DCC7EF098E449A24CF32E778?/saveas/Yi1kbXNjLXNjOjc1OGIwMjRiODYxMzQxMDI5YTQ5OGM1MGMyZWExMmQz/sign/7e1bd9c8bf894a0a731ec49d8b73e08f:1744356567688:9C3F564E93E9CAE877C11A3769216B3E966CCE6F8E4A6D10272EAB6E500E23AC37F4DB5E3809E1307298AAC1952635E4D35458332C51F98D7D6A735A0A396FDB2548590ECA5B4CA2901522C4D0B20AC547CCF395FD0B827033270C57F39B05DDCD99C6AD729CCDDFDAC32C8624E8CE08DBE62E2C57E020AE81A97A8EFD4B84626457E6412C201B2E705AEDCF7903BFD2B67ED1852620F7296A8DA157075998754BDB67E26985CD0B1A23C0EDDED9A23227A804D028090C48CEF9F566629BFBF54C31412C197B6C9D2E6E6D211C2E0FBD9F4E255EB7FBD389153C90F6F34CDE27200367C8EF40DDFF93FB4A22ACA027F26FE0CC9D6CACF5647103D6A548AF89BC2F9A24218F93525824E7673B79050CE0","page":"pages/live/live.html?id=675fcf0f35f03b059ef7b00c&hk_code=9dFgzYbf","mediaId":"306d0201020466306402010002047ada53e402031e903802049ec7f46d020467ce5c690436323036313132383637365f313130303330313839315f32376663363335343830353835363639383735616261636137383832323237300203102800020305a1a004000201010201000400","token":"6A776D6C706477776A6777787172787A","officialId":"gh_f43f003bf939@app","officialName":"云助理live","logoUrl":"http://wx.qlogo.cn/mmhead/Q3auHgzwzM7gzf6yibpKsicv7FYA6cAGibmmhJet9vn24eEQslSFgOISg/0"}'},
                {"type": MessageType.TEXT, "content": "a" * 10000},
                # {"type": MessageType.IMAGE, "content": "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png", "fileName": "test.png"},
                # {"type": MessageType.FILE, "content": "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png", "fileName": "test.png"},
                # {"type": MessageType.FILE,
                #  "content": "https://nrybj.e-chinalife.com/cms/api-object/object/private/7e1bd9c8bf894a0a731ec49d8b73e08f/F90752F8A6924F41EFBA75FD85E44AA04B9EA19180213EA67DDBEB8EEA8BEFE20872C575430B973C1AB31E7D6F1E5927225D4CF8529E7E06F7464ECBBA48F2D3?/saveas/Yi1kbXNjLXNjOjE5MzUyMTZhNTRkZjRkY2M4YWFhZGUzZGM1MDMxNzc0/sign/7e1bd9c8bf894a0a731ec49d8b73e08f:1742976222287:9C3F564E93E9CAE877C11A3769216B3E966CCE6F8E4A6D10272EAB6E500E23AC37F4DB5E3809E1307298AAC1952635E4D35458332C51F98D7D6A735A0A396FDB42E0C41A87924C443CB1C0FD3C084729EA372DD58A7C343ACF8987819CF947D1E68F78323A12AA82BD47B98F9A52D8ED6D6E009040224BD5DFE5B40C889C665AD0BF15262D6A7181DECB4699CD0E7CF7CB2FDF09B2FEDA9252B1BCB61A130266613F4893EA6976B3893A888996295649533F68D9EEEC830A7CA2DC85067CD47C4C31412C197B6C9D2E6E6D211C2E0FBD483D432470A56EBC9396CCB8ECEBBBAB32142AC8F5B373003D5A722EB84CB17AC6FC5BA0FC7E1164837C65DF7C17510697C752CF379DC7914A1214442FCD6EBF",
                #  'fileName': '汇总.xlsx',
                #  'fileType': 'xlsx',}
                # {"type": MessageType.VIDEO, "content": "https://www.xxxx.com/video.mp4"},
                ]
        })


    @APP.print_run_time
    def test_08_send_private_messages(self):
        client = self.get_client()
        client.send_private_messages({
            "target": "not_exist_person",
            "messages": [
                {"type": MessageType.TEXT, "content": "Hey, dude.🐟#0001139"},
                {"type": MessageType.TEXT, "content": "a" * 10000},
                # {"type": MessageType.IMAGE, "content": "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png"},
                # {"type": MessageType.VIDEO, "content": "https://www.xxxx.com/video.mp4"},
                # {"type": MessageType.FILE, "content": "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png"},
                {"type": MessageType.FILE, "content": "http://**************:80/download?url=file:/home/<USER>/RPA/rpa-server/target/rpa-server-1.0.0-SNAPSHOT.jar!/BOOT-INF/temps/file/软考证书有效性.png"}
            ]
        })
    #
    # @APP.print_run_time
    # def test_09_send_multiple_private_messages(self):
    #     client = self.get_client()
    #     for j in range(10):
    #         target = random.choice(['酒醉的蝴蝶', '游泳神', '王倩'])
    #         for i in range(10):
    #             client.send_private_messages({
    #                 "target": target,
    #                 "messages": [
    #                     {"type": MessageType.TEXT, "content": "🐟" + str(i) },
    #                     # {"type": MessageType.IMAGE, "content": "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png"},
    #                     # {"type": MessageType.VIDEO, "content": "https://www.xxxx.com/video.mp4"},
    #                     # {"type": MessageType.FILE, "content": "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png"},
    #                 ]
    #             })
    #
    # def test_10_send_group_messages(self):
    #     client = self.get_client()
    #     client.send_group_messages({
    #         "target": "世界",
    #         "messages": [
    #             {"type": MessageType.TEXT, "content": "Hey, guys.🐟"},
    #             {"type": MessageType.IMAGE, "content": "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png"},
    #             # {"type": MessageType.VIDEO, "content": "https://www.xxxx.com/video.mp4"},
    #             # {"type": MessageType.FILE, "content": "https://www.xxxx.com/file.zip"},
    #             {"type": MessageType.MENTION, "content": "ggbone"},
    #         ]
    #     })

    # def test_10_add_contacts(self):
    #     client = self.get_client()
    #     client.add_contacts({
    #         "contacts": [
    #             {"target": "phone"},
    #             {"target": "phone", "reason": "hello"}
    #         ]
    #     })



def suite():
    suite = unittest.TestSuite()
    loader = unittest.TestLoader()
    suite.addTests(loader.loadTestsFromTestCase(WecomTestSuite))
    return suite

if __name__ == "__main__":
    runner = unittest.TextTestRunner()
    runner.run(suite())