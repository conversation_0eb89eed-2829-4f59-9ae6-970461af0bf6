import sys
import os
import time
import logging
import functools

def print_run_time(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logging.debug(f'{func.__name__} 执行时间: {end_time - start_time:.2f}秒')
        return result
    return wrapper

from .image_clipboard import set_clipboard_data_image
from .file_clipboard import set_clipboard_data_file
from .card_clipboard import set_clipboard_data_card

__all__ = ['set_clipboard_data_image', 'set_clipboard_data_file', 'set_clipboard_data_card', 'set_clipboard_data_miniprogram', 'print_run_time'] 