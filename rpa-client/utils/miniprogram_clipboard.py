import win32clipboard as clipboard
import win32con
from PIL import Image
import io
import struct
import win32gui
import win32ui
import os, time, base64
from . import miniprogram_pb2
import win32api
from . import print_run_time

@print_run_time
def set_clipboard_data_miniprogram(miniprogram_data_json):
   official_id = miniprogram_data_json.get("officialId")
   appId = miniprogram_data_json.get("appid")
   officialName = miniprogram_data_json.get("officialName")
   logoUrl = miniprogram_data_json.get("logoUrl")
   mediaId = miniprogram_data_json.get("mediaId")
   token = miniprogram_data_json.get("token")
   title = miniprogram_data_json.get("title")
   imgUrl = miniprogram_data_json.get("imgUrl")
   page = miniprogram_data_json.get("page")
   if official_id is None or appId is None or officialName is None or logoUrl is None or mediaId is None or token is None or title is None or imgUrl is None or page is None:
       raise Exception("miniprogram Invalid data")
   # 创建 WechatMiniProgramFrame 对象并设置字段
   wechatMiniProgramFrame = miniprogram_pb2.WechatMiniProgramFrame()
   wechatMiniProgramFrame.magic_number = 1033520
   wechatMiniProgramFrame.protocol_flags = 0
   wechatMiniProgramFrame.env_mode = 3
   wechatMiniProgramFrame.reserved1 = 0
   wechatMiniProgramFrame.reserved2 = 0
   wechatMiniProgramFrame.reserved3 = 0
   wechatMiniProgramFrame.crc_check = 78
   # 设置业务消息体
   wechatMiniProgramFrame.payload.app_info.official_account = official_id
   wechatMiniProgramFrame.payload.app_info.app_id = appId
   wechatMiniProgramFrame.payload.app_info.launch_path = page
   wechatMiniProgramFrame.payload.app_info.entry_channel = 2
   wechatMiniProgramFrame.payload.app_info.scene_type = 29
   wechatMiniProgramFrame.payload.app_info.logo_url = logoUrl
   wechatMiniProgramFrame.payload.app_info.page_title = title
   wechatMiniProgramFrame.payload.app_info.field8 = ''
   wechatMiniProgramFrame.payload.app_info.insurance_provider = officialName
   wechatMiniProgramFrame.payload.app_info.transaction_sign = mediaId
   wechatMiniProgramFrame.payload.app_info.session_key = token
   wechatMiniProgramFrame.payload.app_info.device_fp = '27fc635480585669875abaca78822270'
   wechatMiniProgramFrame.payload.app_info.key_version = 32986
   wechatMiniProgramFrame.payload.app_info.currency_type = 0
   wechatMiniProgramFrame.payload.app_info.pay_channel = 0
   wechatMiniProgramFrame.payload.app_info.total_fee = 100
   wechatMiniProgramFrame.payload.app_info.actual_fee = 100
   # wechatMiniProgramFrame.payload.app_info.security.res_download.media_url = 'https://imunion.weixin.qq.com/cgi-bin/mmae-bin/tpdownloadmedia?param=v1_d7d4448095b57c012ca98a8382dc2120c160f99659687dec734d2184e81d4cf2d960f35cbcf2fdebdb997feff60810b4755a9202a0cc0ec84c6bc8dbd629095223241cd5b988920ec0360c1c35a3bf3a503270362915fa399cc51de36aebc7defd6b228c581b2358106a68f06f25dd238bf32e3c74654643ebd75ec06b85696fb63713f3d92fc94687823167d7248162ca53087dc8c70f596308c8aeaee0ae0bfccb49726e49cbfa87dbc7f07bae86ccac60a8c411aee57773f1c744b121a2d2f1af409de7fcd956d60898cc80fb5e5e5e71ee904f9ddb0bd99cea2f497b3d71588a135bdbc0b19104fd2f6b4b8fda5b3507326c9a326c3a7f07a58ce7279473'
   # wechatMiniProgramFrame.payload.app_info.security.res_download.content_md5 = 'ad56ad7b15bb0c151a411eab94c393b6'
   # wechatMiniProgramFrame.payload.app_info.security.res_download.auth_token = 'v1_d7d4448095b57c012ca98a8382dc2120c160f99659687dec734d2184e81d4cf29d4ba05e2d2325ad80dbd3b86509c142771249f86b8772f1d30ea049c1bbd2b3'
   # wechatMiniProgramFrame.payload.app_info.security.res_download.encryption_iv = '27fc635480585669875abaca78822270'
   # wechatMiniProgramFrame.payload.app_info.security.res_download.proto_ver = 32986
   # wechatMiniProgramFrame.payload.app_info.security.sampling_rate = 100
   # wechatMiniProgramFrame.payload.app_info.security.ttl = 100
   wechatMiniProgramFrame.temp_token = ''
   wechatMiniProgramFrame.int11 = 0
   wechatMiniProgramFrame.int12 = 0
   wechatMiniProgramFrame.int13 = 0
   wechatMiniProgramFrame.int14 = 0
   wechatMiniProgramFrame.encrypted_openid = ''
   wechatMiniProgramFrame.debug_info = ''
   # 1396-1485
   # wechatMiniProgramFrame.innerMessage.nestedMessage.field1 = 'bf 77 f1 35 0e 75 76 83 76 a3 91 3e 9f 0a ad 84 a9 91 97 56 da 45 ac 58 60 04 9b 8c 3f cb b0 41'
   # wechatMiniProgramFrame.innerMessage.nestedMessage.field2 = 78
   # wechatMiniProgramFrame.innerMessage.nestedMessage.nestedNestedMessage.field1 = 1741055908
   # wechatMiniProgramFrame.innerMessage.nestedMessage.nestedNestedMessage.field2 = 2
   # wechatMiniProgramFrame.innerMessage.nestedMessage.nestedNestedMessage.field3 = '8329945170011305717'
   # wechatMiniProgramFrame.innerMessage.nestedMessage.nestedNestedMessage.field4.field2 = 2
   # wechatMiniProgramFrame.innerMessage.nestedMessage.nestedNestedMessage.field6 = 1688858041539590
   # wechatMiniProgramFrame.innerMessage.nestedMessage.nestedNestedMessage.field7 = 1
   wechatMiniProgramFrame.int21 = 0
   wechatMiniProgramFrame.int22 = 0
   # 序列化为二进制数据
   serialized_data = wechatMiniProgramFrame.SerializeToString()
   # 输出序列化的二进制数据
   serialized_data = serialized_data + b'0x0a'
   boost_ser = ("22 serialization::archive 12 0 0 1 0 {length} ".format(
       length=len(serialized_data)).encode() + serialized_data).hex()
   # 反序列化
   new_main_message = miniprogram_pb2.WechatMiniProgramFrame()
   new_main_message.ParseFromString(serialized_data)
   url_content = title.encode()
   html_content = b"""<html>\r\n<body>\r\n<!--StartFragment--><a href="">%s</a><!--EndFragment-->\r\n</body>\r\n</html>""" % url_content
   meta_content = b"""Version:0.9\r\nStartHTML:0000000117\r\nEndHTML:0000000425\r\nStartFragment:0000000153\r\nEndFragment:0000000389\r\nSourceURL:\r\n"""
   template_content = meta_content + html_content
   version = "0.9"
   start_html = f"{len(meta_content):0>10}"
   end_html = f"{len(template_content):0>10}"
   start_fragment = f"{template_content.find(b'<!--StartFragment-->') + len(b'<!--StartFragment-->'):0>10}"
   end_fragment = f"{template_content.find(b'<!--EndFragment-->'):0>10}"
   clipboard_format = f"""Version:{version}\r\nStartHTML:{start_html}\r\nEndHTML:{end_html}\r\nStartFragment:{start_fragment}\r\nEndFragment:{end_fragment}\r\nSourceURL:\r\n""".encode() + html_content
   # 打开剪切板
   clipboard.OpenClipboard()
   # 清空剪切板
   clipboard.EmptyClipboard()
   # 设置 HTML Format 格式的内容
   html_format_id = clipboard.RegisterClipboardFormat("HTML Format")
   clipboard.SetClipboardData(html_format_id, clipboard_format)
   # 设置 WeWork Message 格式的内容
   wework_format_id = clipboard.RegisterClipboardFormat("WeWork Message")
   clipboard.SetClipboardData(wework_format_id, bytes.fromhex(boost_ser))
   # 关闭剪切板
   clipboard.CloseClipboard()