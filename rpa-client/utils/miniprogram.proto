syntax = "proto3";

// 主协议容器（对应16-1381字节段）
message WechatMiniProgramFrame {
    /* 协议头（0-16字节） */
    uint32 magic_number = 1;     // 魔数 1033520 (固定标识)
    uint32 protocol_flags = 2;   // 协议标志位
    uint32 env_mode = 3;         // 环境模式 (-2=测试环境)
    uint32 reserved1 = 4;
    uint32 reserved2 = 5;
    uint32 reserved3 = 6;
    uint32 crc_check = 7;        // CRC32校验码

    /* 业务消息体（16-1381） */
    message BusinessPayload {
        AppIdentity app_info = 107;       // 小程序身份信息
    }

    BusinessPayload payload = 8;
    // 会话上下文 1381-1396
    string temp_token = 9;        // 临时令牌
    uint32 int11 = 11;
    uint32 int12 = 12;
    uint32 int13 = 13;
    uint32 int14 = 14;
    string encrypted_openid = 15; // 加密用户标识
    string debug_info = 16;       // 调试信息
    InnerMessage innerMessage = 20;
    uint32 int21 = 21;
    uint32 int22 = 22;
}

/* 小程序身份信息（0-1362字节） */
message AppIdentity {
    string official_account = 1;   // 小程序原始ID gh_3c12e287975c@app
    string app_id = 2;             // 应用ID wxd8248ad5ba816a86
    string launch_path = 3;        // 公众号页面路劲
    uint32 entry_channel = 4;      //
    uint32 scene_type = 5;         //
    string logo_url = 6;           // Logo地址
    string page_title = 7;         // 页面标题
    string field8 = 8;
    string insurance_provider = 10; // 小程序封面展示内容
    string transaction_sign = 12; // 小程序分享图片
    string session_key = 13;      // 获取图片需要的token?
    string device_fp = 14;       //
    uint32 key_version = 15;     //
    uint32 currency_type = 19;  //
    uint32 pay_channel = 20;     //
    uint32 total_fee = 21;       //
    uint32 actual_fee = 22;      //
    SecurityVerification security = 23; //

}

/* 安全验证数据（554-1358字节） */
message SecurityVerification {
    message ResourceDownload {
        string media_url = 1;     //
        string content_md5 = 2;   //
        string auth_token = 3;    //
        string encryption_iv = 4; //
        uint32 proto_ver = 5;     //
    }

    ResourceDownload res_download = 1;
    uint32 sampling_rate = 2; //
    uint32 ttl = 3;           //
}

// 1396-1485
message InnerMessage {
        message NestedMessage {
             message NestedNestedMessage {
                uint64 field1 = 1;
                uint32 field2 = 2;
                string field3 = 3;
                NestedNestedNestedMessage field4 = 4;
                uint64 field6 = 6;
                uint32 field7 = 7;
                message NestedNestedNestedMessage {
                    uint32 field2 = 2;
                }
            }
            string field1 = 1;
            uint32 field2 = 2;
            NestedNestedMessage nestedNestedMessage = 3;

        }
    NestedMessage nestedMessage = 3045;
    }
