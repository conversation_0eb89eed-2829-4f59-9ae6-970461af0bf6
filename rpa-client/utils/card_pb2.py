# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: utils/card.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10utils/card.proto\"\xeb\x02\n\x0bMainMessage\x12\x0e\n\x06\x66ield1\x18\x01 \x01(\x04\x12\x0e\n\x06\x66ield2\x18\x02 \x01(\x04\x12\x0e\n\x06\x66ield3\x18\x03 \x01(\x04\x12\x0e\n\x06sender\x18\x04 \x01(\x04\x12\x10\n\x08receiver\x18\x05 \x01(\x04\x12\x0e\n\x06\x66ield6\x18\x06 \x01(\x04\x12\x0e\n\x06\x66ield7\x18\x07 \x01(\x05\x12-\n\x12\x62\x61sic_card_message\x18\x08 \x01(\x0b\x32\x11.BasicCardMessage\x12\x0e\n\x06\x66ield9\x18\t \x01(\t\x12\x0f\n\x07\x66ield11\x18\x0b \x01(\x04\x12\x11\n\ttimestamp\x18\x0c \x01(\x04\x12\x0f\n\x07\x66ield13\x18\r \x01(\x04\x12\x0f\n\x07\x66ield14\x18\x0e \x01(\x04\x12\x10\n\x08\x61pp_info\x18\x0f \x01(\t\x12\x0f\n\x07\x66ield16\x18\x10 \x01(\t\x12 \n\x07\x66ield20\x18\x14 \x01(\x0b\x32\x0f.NestedMessage2\x12\x0f\n\x07\x66ield21\x18\x15 \x01(\x04\x12\x0f\n\x07\x66ield22\x18\x16 \x01(\x04\"Z\n\x10\x42\x61sicCardMessage\x12\x10\n\x08\x63\x61rd_url\x18\x01 \x01(\t\x12\x14\n\x0c\x63\x61rd_img_url\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x04 \x01(\t\"X\n\x0eNestedMessage2\x12\x1e\n\ttimestamp\x18\xf4\x07 \x01(\x0b\x32\n.Timestamp\x12&\n\tfield3045\x18\xe5\x17 \x01(\x0b\x32\x12.NestedMessage3045\"!\n\tTimestamp\x12\x14\n\x0ctimestamp_ms\x18\x17 \x01(\x04\"Z\n\x11NestedMessage3045\x12\x0c\n\x04hash\x18\x01 \x01(\x0c\x12\x0e\n\x06\x66ield2\x18\x02 \x01(\x05\x12\'\n\x06\x66ield3\x18\x03 \x01(\x0b\x32\x17.NestedMessage3045Inner\"\x96\x01\n\x16NestedMessage3045Inner\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0e\n\x06\x66ield2\x18\x02 \x01(\x05\x12\x10\n\x08\x61pp_info\x18\x03 \x01(\t\x12\'\n\x06\x66ield4\x18\x04 \x01(\x0b\x32\x17.NestedMostInnerMessage\x12\x0e\n\x06sender\x18\x06 \x01(\x04\x12\x0e\n\x06\x66ield7\x18\x07 \x01(\x05\"(\n\x16NestedMostInnerMessage\x12\x0e\n\x06\x66ield2\x18\x02 \x01(\x04\"~\n\nAppInfoMes\x12\x10\n\x03val\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12\x11\n\x04time\x18\x02 \x01(\x04H\x01\x88\x01\x01\x12\x13\n\x06userid\x18\x03 \x01(\x04H\x02\x88\x01\x01\x12\x11\n\x04type\x18\x04 \x01(\rH\x03\x88\x01\x01\x42\x06\n\x04_valB\x07\n\x05_timeB\t\n\x07_useridB\x07\n\x05_typeb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'utils.card_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _MAINMESSAGE._serialized_start=21
  _MAINMESSAGE._serialized_end=384
  _BASICCARDMESSAGE._serialized_start=386
  _BASICCARDMESSAGE._serialized_end=476
  _NESTEDMESSAGE2._serialized_start=478
  _NESTEDMESSAGE2._serialized_end=566
  _TIMESTAMP._serialized_start=568
  _TIMESTAMP._serialized_end=601
  _NESTEDMESSAGE3045._serialized_start=603
  _NESTEDMESSAGE3045._serialized_end=693
  _NESTEDMESSAGE3045INNER._serialized_start=696
  _NESTEDMESSAGE3045INNER._serialized_end=846
  _NESTEDMOSTINNERMESSAGE._serialized_start=848
  _NESTEDMOSTINNERMESSAGE._serialized_end=888
  _APPINFOMES._serialized_start=890
  _APPINFOMES._serialized_end=1016
# @@protoc_insertion_point(module_scope)
