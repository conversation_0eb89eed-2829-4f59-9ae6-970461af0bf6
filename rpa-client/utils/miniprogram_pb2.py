# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: utils/miniprogram.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17utils/miniprogram.proto\"\xd2\x03\n\x16WechatMiniProgramFrame\x12\x14\n\x0cmagic_number\x18\x01 \x01(\r\x12\x16\n\x0eprotocol_flags\x18\x02 \x01(\r\x12\x10\n\x08\x65nv_mode\x18\x03 \x01(\r\x12\x11\n\treserved1\x18\x04 \x01(\r\x12\x11\n\treserved2\x18\x05 \x01(\r\x12\x11\n\treserved3\x18\x06 \x01(\r\x12\x11\n\tcrc_check\x18\x07 \x01(\r\x12\x38\n\x07payload\x18\x08 \x01(\x0b\x32\'.WechatMiniProgramFrame.BusinessPayload\x12\x12\n\ntemp_token\x18\t \x01(\t\x12\r\n\x05int11\x18\x0b \x01(\r\x12\r\n\x05int12\x18\x0c \x01(\r\x12\r\n\x05int13\x18\r \x01(\r\x12\r\n\x05int14\x18\x0e \x01(\r\x12\x18\n\x10\x65ncrypted_openid\x18\x0f \x01(\t\x12\x12\n\ndebug_info\x18\x10 \x01(\t\x12#\n\x0cinnerMessage\x18\x14 \x01(\x0b\x32\r.InnerMessage\x12\r\n\x05int21\x18\x15 \x01(\r\x12\r\n\x05int22\x18\x16 \x01(\r\x1a\x31\n\x0f\x42usinessPayload\x12\x1e\n\x08\x61pp_info\x18k \x01(\x0b\x32\x0c.AppIdentity\"\x9c\x03\n\x0b\x41ppIdentity\x12\x18\n\x10official_account\x18\x01 \x01(\t\x12\x0e\n\x06\x61pp_id\x18\x02 \x01(\t\x12\x13\n\x0blaunch_path\x18\x03 \x01(\t\x12\x15\n\rentry_channel\x18\x04 \x01(\r\x12\x12\n\nscene_type\x18\x05 \x01(\r\x12\x10\n\x08logo_url\x18\x06 \x01(\t\x12\x12\n\npage_title\x18\x07 \x01(\t\x12\x0e\n\x06\x66ield8\x18\x08 \x01(\t\x12\x1a\n\x12insurance_provider\x18\n \x01(\t\x12\x18\n\x10transaction_sign\x18\x0c \x01(\t\x12\x13\n\x0bsession_key\x18\r \x01(\t\x12\x11\n\tdevice_fp\x18\x0e \x01(\t\x12\x13\n\x0bkey_version\x18\x0f \x01(\r\x12\x15\n\rcurrency_type\x18\x13 \x01(\r\x12\x13\n\x0bpay_channel\x18\x14 \x01(\r\x12\x11\n\ttotal_fee\x18\x15 \x01(\r\x12\x12\n\nactual_fee\x18\x16 \x01(\r\x12\'\n\x08security\x18\x17 \x01(\x0b\x32\x15.SecurityVerification\"\xf2\x01\n\x14SecurityVerification\x12<\n\x0cres_download\x18\x01 \x01(\x0b\x32&.SecurityVerification.ResourceDownload\x12\x15\n\rsampling_rate\x18\x02 \x01(\r\x12\x0b\n\x03ttl\x18\x03 \x01(\r\x1ax\n\x10ResourceDownload\x12\x11\n\tmedia_url\x18\x01 \x01(\t\x12\x13\n\x0b\x63ontent_md5\x18\x02 \x01(\t\x12\x12\n\nauth_token\x18\x03 \x01(\t\x12\x15\n\rencryption_iv\x18\x04 \x01(\t\x12\x11\n\tproto_ver\x18\x05 \x01(\r\"\xb3\x03\n\x0cInnerMessage\x12\x33\n\rnestedMessage\x18\xe5\x17 \x01(\x0b\x32\x1b.InnerMessage.NestedMessage\x1a\xed\x02\n\rNestedMessage\x12\x0e\n\x06\x66ield1\x18\x01 \x01(\t\x12\x0e\n\x06\x66ield2\x18\x02 \x01(\r\x12L\n\x13nestedNestedMessage\x18\x03 \x01(\x0b\x32/.InnerMessage.NestedMessage.NestedNestedMessage\x1a\xed\x01\n\x13NestedNestedMessage\x12\x0e\n\x06\x66ield1\x18\x01 \x01(\x04\x12\x0e\n\x06\x66ield2\x18\x02 \x01(\r\x12\x0e\n\x06\x66ield3\x18\x03 \x01(\t\x12Y\n\x06\x66ield4\x18\x04 \x01(\x0b\x32I.InnerMessage.NestedMessage.NestedNestedMessage.NestedNestedNestedMessage\x12\x0e\n\x06\x66ield6\x18\x06 \x01(\x04\x12\x0e\n\x06\x66ield7\x18\x07 \x01(\r\x1a+\n\x19NestedNestedNestedMessage\x12\x0e\n\x06\x66ield2\x18\x02 \x01(\rb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'utils.miniprogram_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _WECHATMINIPROGRAMFRAME._serialized_start=28
  _WECHATMINIPROGRAMFRAME._serialized_end=494
  _WECHATMINIPROGRAMFRAME_BUSINESSPAYLOAD._serialized_start=445
  _WECHATMINIPROGRAMFRAME_BUSINESSPAYLOAD._serialized_end=494
  _APPIDENTITY._serialized_start=497
  _APPIDENTITY._serialized_end=909
  _SECURITYVERIFICATION._serialized_start=912
  _SECURITYVERIFICATION._serialized_end=1154
  _SECURITYVERIFICATION_RESOURCEDOWNLOAD._serialized_start=1034
  _SECURITYVERIFICATION_RESOURCEDOWNLOAD._serialized_end=1154
  _INNERMESSAGE._serialized_start=1157
  _INNERMESSAGE._serialized_end=1592
  _INNERMESSAGE_NESTEDMESSAGE._serialized_start=1227
  _INNERMESSAGE_NESTEDMESSAGE._serialized_end=1592
  _INNERMESSAGE_NESTEDMESSAGE_NESTEDNESTEDMESSAGE._serialized_start=1355
  _INNERMESSAGE_NESTEDMESSAGE_NESTEDNESTEDMESSAGE._serialized_end=1592
  _INNERMESSAGE_NESTEDMESSAGE_NESTEDNESTEDMESSAGE_NESTEDNESTEDNESTEDMESSAGE._serialized_start=1549
  _INNERMESSAGE_NESTEDMESSAGE_NESTEDNESTEDMESSAGE_NESTEDNESTEDNESTEDMESSAGE._serialized_end=1592
# @@protoc_insertion_point(module_scope)
