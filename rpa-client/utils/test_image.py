import win32clipboard as clipboard
import win32con
from PIL import Image
import io
import struct
import win32gui
import win32ui
import os
import image_pb2
import win32api

def set_clipboard_data_image(image_path=None):
    """
    按照指定顺序设置剪贴板数据：
    1. WeChat_RichEdit_Format (49588)
    2. HTML Format (49297)
    3. WeWork Message (49581)
    4. CF_BITMAP (2)
    5. CF_HDROP (15)
    6. Preferred DropEffect (49298)
    7. Logical Performed DropEffect (49300)
    """
    try:
        clipboard.OpenClipboard()
        clipboard.EmptyClipboard()

        # 1. 设置 WeChat_RichEdit_Format (ID: 49588)
        wechat_richedit_id = clipboard.RegisterClipboardFormat("WeChat_RichEdit_Format")
        richedit_data = generate_richedit_data(image_path)  # 生成富文本数据
        clipboard.SetClipboardData(wechat_richedit_id, richedit_data)

        # 2. 设置 HTML Format (ID: 49297)
        html_format_id = clipboard.RegisterClipboardFormat("HTML Format")
        html_content = generate_html_content(image_path)  # 生成HTML内容
        clipboard.SetClipboardData(html_format_id, html_content.encode('utf-8'))

        # 3. 设置 WeWork Message (ID: 49581)
        wework_msg_id = clipboard.RegisterClipboardFormat("WeWork Message")
        wework_message = generate_wework_message(image_path)  # 生成企业微信消息
        clipboard.SetClipboardData(wework_msg_id, wework_message)

        # 4. 设置 CF_BITMAP (ID: 2)
        if image_path:
            clipboard.SetClipboardData(win32con.CF_BITMAP, "")
            # bitmap_handle = create_bitmap_handle(image_path)
            # if bitmap_handle:
            #     clipboard.SetClipboardData(win32con.CF_BITMAP, bitmap_handle)

        # 5. 设置 CF_HDROP (ID: 15)
        if image_path:
            hdrop_data = create_hdrop_data([image_path])
            clipboard.SetClipboardData(win32con.CF_HDROP, hdrop_data)

        # 6. 设置 Preferred DropEffect (ID: 49298)
        preferred_effect_id = clipboard.RegisterClipboardFormat("Preferred DropEffect")
        clipboard.SetClipboardData(preferred_effect_id, struct.pack('I', 1))

        # 7. 设置 Logical Performed DropEffect (ID: 49300)
        logical_effect_id = clipboard.RegisterClipboardFormat("Logical Performed DropEffect")
        clipboard.SetClipboardData(logical_effect_id, struct.pack('I', 1))

    finally:
        clipboard.CloseClipboard()

def create_bitmap_handle(image_path):
    """
    创建位图句柄
    """
    try:
        image = Image.open(image_path)
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        width, height = image.size
        
        # 创建设备上下文
        screen_dc = win32gui.GetDC(0)
        dc = win32ui.CreateDCFromHandle(screen_dc)
        mem_dc = dc.CreateCompatibleDC()
        
        # 创建位图
        bitmap = win32ui.CreateBitmap()
        bitmap.CreateCompatibleBitmap(dc, width, height)
        
        # 选择位图
        old_bitmap = mem_dc.SelectObject(bitmap)
        
        # 将PIL图像转换为位图数据
        pixels = image.load()
        for x in range(width):
            for y in range(height):
                r, g, b = pixels[x, y]
                mem_dc.SetPixel(x, y, win32api.RGB(r, g, b))
        
        # 清理
        mem_dc.SelectObject(old_bitmap)
        win32gui.ReleaseDC(0, screen_dc)
        
        return bitmap.GetHandle()
    except Exception as e:
        print(f"创建位图失败: {str(e)}")
        return None

def create_hdrop_data(file_paths):
    """
    创建文件拖放数据
    """
    from ctypes import Structure, sizeof, c_uint32
    
    class DROPFILES(Structure):
        _fields_ = [
            ('pFiles', c_uint32),
            ('pt', c_uint32 * 2),
            ('fNC', c_uint32),
            ('fWide', c_uint32),
        ]
    
    offset = sizeof(DROPFILES)
    files_string = '\0'.join(file_paths) + '\0\0'
    files_buffer = files_string.encode('utf-16le')
    
    df = DROPFILES()
    df.pFiles = offset
    df.pt[0] = 0
    df.pt[1] = 0
    df.fNC = 0
    df.fWide = 1
    
    return bytes(df) + files_buffer

def generate_html_content(image_path):
    """
    生成HTML格式内容
    """
    html = """
<html>
<body>
<!--StartFragment-->
<img src="{image_path}"/>
<!--EndFragment-->
</body>
</html>""".format(image_path=image_path)
    
    # 计算偏移量
    source = "Version:0.9\r\n"
    source += "StartHTML:{:0>10}\r\n"
    source += "EndHTML:{:0>10}\r\n"
    source += "StartFragment:{:0>10}\r\n"
    source += "EndFragment:{:0>10}\r\n"
    source += "SourceURL:\r\n"
    
    html_length = len(source) + len(html)
    fragment_start = html.find('<!--StartFragment-->') + len('<!--StartFragment-->')
    fragment_end = html.find('<!--EndFragment-->')
    
    return source.format(
        len(source),
        html_length,
        fragment_start + len(source),
        fragment_end + len(source)
    ) + html

def get_image_resolution(image_path):
    """
    获取图片的分辨率
    """
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            return width, height
    except Exception as e:
        print(f"获取图片分辨率失败: {str(e)}")
        return 0, 0


def generate_wework_message(image_path):
    """
    生成企业微信消息数据
    """
    # 创建外层消息
    outer_msg = image_pb2.OuterMessage()
    
    # 设置基本字段
    outer_msg.field1 = 0
    outer_msg.field2 = 0
    outer_msg.field3 = 3
    outer_msg.field4 = 0
    outer_msg.field5 = 0
    outer_msg.field6 = 0
    outer_msg.field7 = 7
    
    # 设置内层消息
    inner_msg = outer_msg.field8
    inner_msg.field2 = os.path.basename(image_path)
    # 获取图片分辨率
    width, height = get_image_resolution(image_path)
    inner_msg.field5 = width
    inner_msg.field6 = height
    inner_msg.field100 = image_path
    
    # 设置其他字段
    outer_msg.field9 = ""  # 空字符串
    outer_msg.field11 = 0
    outer_msg.field12 = 0
    outer_msg.field13 = 0
    outer_msg.field14 = 0
    outer_msg.field15 = b""  # 空字节
    outer_msg.field16 = b""  # 空字节
    outer_msg.field20 = b""  # 空字节
    outer_msg.field21 = 0
    outer_msg.field22 = 0
    serialized_data = outer_msg.SerializeToString()
    # 输出序列化的二进制数据
    serialized_data = serialized_data
    boost_ser = ("22 serialization::archive 19 0 0 1 0 {length} ".format(
        length=len(serialized_data)).encode() + serialized_data).hex() + '0A'
    return bytes.fromhex(boost_ser)

def generate_richedit_data(image_path):
    """
    生成微信富文本数据
    """
    # 这里需要根据实际需求生成富文本数据
    return '<WeChatRichEditFormat><EditElement type="1" filepath = "{image_path}" /></WeChatRichEditFormat>'.format(image_path=image_path).encode('utf-8')

if __name__ == "__main__":
    try:
        # 测试示例
        # image_path = r"\\Mac\Home\Pictures\企业微信截图_1742540954579.png"
        image_path = r"\\Mac\Home\Documents\CVE-2016-1000027安全漏洞分析和解决方案.pdf"
        set_clipboard_data_image(image_path)
        print("剪贴板数据设置成功")
    except Exception as e:
        print(f"设置剪贴板失败: {str(e)}") 