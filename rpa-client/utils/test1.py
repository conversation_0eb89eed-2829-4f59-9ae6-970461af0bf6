import miniprogram_pb2
import time
import win32clipboard as clipboard


# 创建 MainMessage 对象并设置字段
wechatMiniProgramFrame = miniprogram_pb2.WechatMiniProgramFrame()
# 设置协议头
wechatMiniProgramFrame.magic_number = 1033520
wechatMiniProgramFrame.protocol_flags = 0
wechatMiniProgramFrame.env_mode = 3
wechatMiniProgramFrame.reserved1 = 0
wechatMiniProgramFrame.reserved2 = 0
wechatMiniProgramFrame.reserved3 = 0
wechatMiniProgramFrame.crc_check = 78

# 设置业务消息体
# wechatMiniProgramFrame.payload.app_info.official_account = 'gh_3c12e287975c@app'
wechatMiniProgramFrame.payload.app_info.official_account = 'gh_f43f003bf939@app'
# wechatMiniProgramFrame.payload.app_info.app_id = 'wxd8248ad5ba816a86'
wechatMiniProgramFrame.payload.app_info.app_id = 'wx4f5dd50093b84881'
# wechatMiniProgramFrame.payload.app_info.launch_path = 'pages/index/index.html?positionId=/pages/policy/policy'
# wechatMiniProgramFrame.payload.app_info.launch_path = 'pages/index/index.html?positionId=/pages/claimReport/claimReport'
# wechatMiniProgramFrame.payload.app_info.launch_path = 'pages/index/index.html?positionId=/pages/myEquity/myEquity'
wechatMiniProgramFrame.payload.app_info.launch_path = 'pages/live/live.html?id=675fcf0f35f03b059ef7b00c&hk_code=9dFgzYbf'
wechatMiniProgramFrame.payload.app_info.entry_channel = 2
wechatMiniProgramFrame.payload.app_info.scene_type = 29
# wechatMiniProgramFrame.payload.app_info.logo_url = 'http://wx.qlogo.cn/mmhead/PFPUMLY8F76KQbfiaJfuyJXAYVzVVmwSrBbuBC87Hyd8qWZAy2DCmxmxFribwC7u5O7x0zumX1fSY/96'
wechatMiniProgramFrame.payload.app_info.logo_url = 'http://wx.qlogo.cn/mmhead/Q3auHgzwzM7gzf6yibpKsicv7FYA6cAGibmmhJet9vn24eEQslSFgOISg/0'
# wechatMiniProgramFrame.payload.app_info.page_title = '我的保单'
# wechatMiniProgramFrame.payload.app_info.page_title = '理赔服务'
# wechatMiniProgramFrame.payload.app_info.page_title = '我的权益'
# wechatMiniProgramFrame.payload.app_info.page_title = '个人养老金政策解读'
wechatMiniProgramFrame.payload.app_info.page_title = '云助理live'
wechatMiniProgramFrame.payload.app_info.field8 = ''
wechatMiniProgramFrame.payload.app_info.insurance_provider = '中国人寿寿险'
# wechatMiniProgramFrame.payload.app_info.transaction_sign = '306d0201020466306402010002047ada53e402031e90380204f0c4f46d020467c667a40436323036313132383637365f313737343532363531305f3731646363643431343163656462343532633536653832386630373166393461020310200002030080e004000201010201000400'
# wechatMiniProgramFrame.payload.app_info.transaction_sign = '306c0201020465306302010002047ada53e402031e90380204ecc7f46d020467c803620435323036313132383637365f3134313334373636395f65636566323235323965373834616366613335363037343532656537333763380203102800020300816004000201010201000400'
# wechatMiniProgramFrame.payload.app_info.transaction_sign = '306b0201020464306202010002047ada53e402031e90380204dfc6f46d020467c804d00435323036313132383637365f3133383433333933345f376238346561643433373266373362383464356134323364383031343930313602031028000202554004000201010201000400'
wechatMiniProgramFrame.payload.app_info.transaction_sign = '306d0201020466306402010002047ada53e402031e903802049ec7f46d020467ce5c690436323036313132383637365f313130303330313839315f32376663363335343830353835363639383735616261636137383832323237300203102800020305a1a004000201010201000400'
# wechatMiniProgramFrame.payload.app_info.session_key = '687766756E78697064627573616C6276'
# wechatMiniProgramFrame.payload.app_info.session_key = '7061667664626A6961767A7565787574'
# wechatMiniProgramFrame.payload.app_info.session_key = '746E686F64786F626A69646564767971'
wechatMiniProgramFrame.payload.app_info.session_key = '6A776D6C706477776A6777787172787A'
# wechatMiniProgramFrame.payload.app_info.device_fp = '71dccd4141cedb452c56e828f071f94a'
# wechatMiniProgramFrame.payload.app_info.device_fp = 'ecef22529e784acfa35607452ee737c8'
# wechatMiniProgramFrame.payload.app_info.device_fp = '7b84ead4372f73b84d5a423d80149016'
wechatMiniProgramFrame.payload.app_info.device_fp = '27fc635480585669875abaca78822270'
wechatMiniProgramFrame.payload.app_info.key_version = 32986
# wechatMiniProgramFrame.payload.app_info.key_version = 33105
# wechatMiniProgramFrame.payload.app_info.key_version = 21819

wechatMiniProgramFrame.payload.app_info.currency_type = 0
wechatMiniProgramFrame.payload.app_info.pay_channel = 0
wechatMiniProgramFrame.payload.app_info.total_fee = 100
wechatMiniProgramFrame.payload.app_info.actual_fee = 100

# wechatMiniProgramFrame.payload.app_info.security.res_download.media_url = 'https://imunion.weixin.qq.com/cgi-bin/mmae-bin/tpdownloadmedia?param=v1_d7d4448095b57c012ca98a8382dc2120c160f99659687dec734d2184e81d4cf236527a09799ffb1b03cc11282e73f591f383a3b29b61197669ace84535521be6abd273f74640692bcef3eb817c58a1fa49cfe32444d26c22774c378f3fb55f6d6324455117006da544593cce80fffee59dc46316bb113e2a6bcc8a62a8802f6cebf6503a70abda70edab676ff52c38993fef542351ecff8780a87f193afab16e805e5f4977102034e38bf5da9e64f17c9c56d7f7899a34e1be2507354fdad9b64b419d98451b560539cc9e966311d0ccdad2158eaf6eef7214d8803dd8fd4df181d239021c6cec0cef2d403afc74849fb7d49443e51cc1ab3ecc8d1870e9e407'
# wechatMiniProgramFrame.payload.app_info.security.res_download.media_url = 'https://imunion.weixin.qq.com/cgi-bin/mmae-bin/tpdownloadmedia?param=v1_d7d4448095b57c012ca98a8382dc2120c160f99659687dec734d2184e81d4cf2c76267e452407516c5caec7bd3ad0be71172c73c6fbac13029dda456dd2aaec320b53244e1c8bc34f86943088de93e89f8af8ddd987b8e558b8ffa7a460e9f1e2fbc348fb9d0d4507f9d4800805241311a3f42f32804697ed6200dcb3faa10c763a1faf31db8de96c5fe6aea858d6cc227772ca038f8a4694581ae176d2606e22005e8bf5af2c00b9ffe4694fc1c5ede38a9c4c12e6d228538a7fcb8c168548663d5f03a9b00c77d480baa905779dabb5178e15fce318eec447f9cb043de788fe5e89116a898c77118e9e23cabaa335cc884d52c27096d1b6969a3e5a5fc1d9a'
# wechatMiniProgramFrame.payload.app_info.security.res_download.media_url = 'https://imunion.weixin.qq.com/cgi-bin/mmae-bin/tpdownloadmedia?param=v1_d7d4448095b57c012ca98a8382dc2120c160f99659687dec734d2184e81d4cf2b6cb489e8368314d05254101b7ac0aee0dbad93877972c2cbc9afdca2b8c47e353124d88d6b02705a92c46c0754d26f478e289519b6f9d11afd59443e8b844ba0ca1ceb74c87c0e1751a10b8684d91f0b564559c4a0a54eb2a180b9ae93cd724f5e77c6faabb3d0e689174cf12f8f8d48cbd831cf11a0b809edeac37d044f8899febb7dcfb3afd015c534364f4116c78e566881857a71856c0dc7270fa5da3e21ad04b5325b264e2af97463cb30aa508a71bd5cbb5d9b65dc4441d9613d83e3ffb35e551aa8189c0b6b99984321ab60e3cb145ad5a971377912117cd51af8858'
# wechatMiniProgramFrame.payload.app_info.security.res_download.media_url = 'https://imunion.weixin.qq.com/cgi-bin/mmae-bin/tpdownloadmedia?param=v1_d7d4448095b57c012ca98a8382dc2120c160f99659687dec734d2184e81d4cf2d960f35cbcf2fdebdb997feff60810b4755a9202a0cc0ec84c6bc8dbd629095223241cd5b988920ec0360c1c35a3bf3a503270362915fa399cc51de36aebc7defd6b228c581b2358106a68f06f25dd238bf32e3c74654643ebd75ec06b85696fb63713f3d92fc94687823167d7248162ca53087dc8c70f596308c8aeaee0ae0bfccb49726e49cbfa87dbc7f07bae86ccac60a8c411aee57773f1c744b121a2d2f1af409de7fcd956d60898cc80fb5e5e5e71ee904f9ddb0bd99cea2f497b3d71588a135bdbc0b19104fd2f6b4b8fda5b3507326c9a326c3a7f07a58ce7279473'
# wechatMiniProgramFrame.payload.app_info.security.res_download.content_md5 = 'eac88a062cbc7e04284c36cc11e695d5'
# wechatMiniProgramFrame.payload.app_info.security.res_download.content_md5 = '2bbab36f848284e2ff0ff255cc1b252b'
# wechatMiniProgramFrame.payload.app_info.security.res_download.content_md5 = '8049d2494addf12e8525bd42c746669f'
# wechatMiniProgramFrame.payload.app_info.security.res_download.content_md5 = 'ad56ad7b15bb0c151a411eab94c393b6'
# wechatMiniProgramFrame.payload.app_info.security.res_download.auth_token = 'v1_d7d4448095b57c012ca98a8382dc2120c160f99659687dec734d2184e81d4cf24401d6569ea355bdbc212d22a9f35bfa0cbed43c3c4780e3cac867c396e245ae'
# wechatMiniProgramFrame.payload.app_info.security.res_download.auth_token = 'v1_d7d4448095b57c012ca98a8382dc2120c160f99659687dec734d2184e81d4cf2e0de0977506c5b57634038cfa7bca01054466ecb0b1982ca39007741c85f3999'
# wechatMiniProgramFrame.payload.app_info.security.res_download.auth_token = 'v1_d7d4448095b57c012ca98a8382dc2120c160f99659687dec734d2184e81d4cf29ade96c6c1c80a3d6005bfc79dea5766a32b76a3a697685e67c31bc46fef857a'
# wechatMiniProgramFrame.payload.app_info.security.res_download.auth_token = 'v1_d7d4448095b57c012ca98a8382dc2120c160f99659687dec734d2184e81d4cf29d4ba05e2d2325ad80dbd3b86509c142771249f86b8772f1d30ea049c1bbd2b3'
# wechatMiniProgramFrame.payload.app_info.security.res_download.encryption_iv = '71dccd4141cedb452c56e828f071f94a'
# wechatMiniProgramFrame.payload.app_info.security.res_download.encryption_iv = 'ecef22529e784acfa35607452ee737c8'
# wechatMiniProgramFrame.payload.app_info.security.res_download.encryption_iv = '7b84ead4372f73b84d5a423d80149016'
# wechatMiniProgramFrame.payload.app_info.security.res_download.encryption_iv = '27fc635480585669875abaca78822270'
# wechatMiniProgramFrame.payload.app_info.security.res_download.proto_ver = 32986
# wechatMiniProgramFrame.payload.app_info.security.res_download.proto_ver = 33105
# wechatMiniProgramFrame.payload.app_info.security.res_download.proto_ver = 21819
# wechatMiniProgramFrame.payload.app_info.security.sampling_rate = 100
# wechatMiniProgramFrame.payload.app_info.security.ttl = 100

wechatMiniProgramFrame.temp_token = ''
wechatMiniProgramFrame.int11 = 0
wechatMiniProgramFrame.int12 = 0
wechatMiniProgramFrame.int13 = 0
wechatMiniProgramFrame.int14 = 0
wechatMiniProgramFrame.encrypted_openid = ''
wechatMiniProgramFrame.debug_info = ''

# 1396-1485
# wechatMiniProgramFrame.innerMessage.nestedMessage.field1 = 'bf 77 f1 35 0e 75 76 83 76 a3 91 3e 9f 0a ad 84 a9 91 97 56 da 45 ac 58 60 04 9b 8c 3f cb b0 41'
# wechatMiniProgramFrame.innerMessage.nestedMessage.field2 = 78
# wechatMiniProgramFrame.innerMessage.nestedMessage.nestedNestedMessage.field1 = 1741055908
# wechatMiniProgramFrame.innerMessage.nestedMessage.nestedNestedMessage.field2 = 2
# wechatMiniProgramFrame.innerMessage.nestedMessage.nestedNestedMessage.field3 = '8329945170011305717'
# wechatMiniProgramFrame.innerMessage.nestedMessage.nestedNestedMessage.field4.field2 = 2
# wechatMiniProgramFrame.innerMessage.nestedMessage.nestedNestedMessage.field6 = 1688858041539590
# wechatMiniProgramFrame.innerMessage.nestedMessage.nestedNestedMessage.field7 = 1

wechatMiniProgramFrame.int21 = 0
wechatMiniProgramFrame.int22 = 0

# 序列化为二进制数据
serialized_data = wechatMiniProgramFrame.SerializeToString()

# 输出序列化的二进制数据
serialized_data = serialized_data + b'0x0a'
print(len(serialized_data))
print("22 serialization::archive 12 0 0 1 0 {length} ".format(length=len(serialized_data)).encode() + serialized_data)
boost_ser = ("22 serialization::archive 12 0 0 1 0 {length} ".format(length=len(serialized_data)).encode() + serialized_data).hex()
print(boost_ser)


# 反序列化
new_main_message = miniprogram_pb2.WechatMiniProgramFrame()
new_main_message.ParseFromString(serialized_data)

# 打印反序列化后的内容
print(new_main_message)

url_content = '我的权益'.encode()
html_content = b"""<html>\r\n<body>\r\n<!--StartFragment--><a href="">%s</a><!--EndFragment-->\r\n</body>\r\n</html>""" % url_content

meta_content = b"""Version:0.9\r\nStartHTML:0000000117\r\nEndHTML:0000000425\r\nStartFragment:0000000153\r\nEndFragment:0000000389\r\nSourceURL:\r\n"""

template_content = meta_content + html_content

version = "0.9"
start_html = f"{len(meta_content):0>10}"
end_html = f"{len(template_content):0>10}"
start_fragment = f"{template_content.find(b'<!--StartFragment-->') + len(b'<!--StartFragment-->'):0>10}"
end_fragment = f"{template_content.find(b'<!--EndFragment-->'):0>10}"

clipboard_format = f"""Version:{version}\r\nStartHTML:{start_html}\r\nEndHTML:{end_html}\r\nStartFragment:{start_fragment}\r\nEndFragment:{end_fragment}\r\nSourceURL:\r\n""".encode() + html_content
# 打开剪切板
clipboard.OpenClipboard()

# 清空剪切板
clipboard.EmptyClipboard()
# 设置 HTML Format 格式的内容
html_format_id = clipboard.RegisterClipboardFormat("HTML Format")
clipboard.SetClipboardData(html_format_id, clipboard_format)

# 设置 WeWork Message 格式的内容
wework_format_id = clipboard.RegisterClipboardFormat("WeWork Message")
clipboard.SetClipboardData(wework_format_id, bytes.fromhex(boost_ser))
# 关闭剪切板
clipboard.CloseClipboard()

print('ok')

