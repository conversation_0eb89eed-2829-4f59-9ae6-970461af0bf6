syntax = "proto3";

message MainMessage {
  uint64 field1 = 1; // 整数值 1006228
  uint64 field2 = 2; // 整数值 9889277
  uint64 field3 = 3; // 整数值 0
  uint64 sender = 4; // 整数值 1688855203510548
  uint64 receiver = 5; // 整数值 1688855203510548
  uint64 field6 = 6; // 整数值 0
  int32 field7 = 7;  // 整数值 13

  BasicCardMessage basic_card_message = 8; // 嵌套消息
  string field9 = 9; // 空字符串
  uint64 field11 = 11; // 整数值 0
  uint64 timestamp = 12; // 整数值 1727337061
  uint64 field13 = 13; // 整数值 0
  uint64 field14 = 14; // 整数值 65537
  string app_info = 15; // "CAEQ5KTUtwYYlPLt85OAgAMgIw=="
  string field16 = 16; // 空字符串

  NestedMessage2 field20 = 20; // 嵌套消息
  uint64 field21 = 21; // 整数值 0
  uint64 field22 = 22; // 整数值 0
}

message BasicCardMessage {
  string card_url = 1; // 卡片 URL
  string card_img_url = 2; // 卡片图片 URL
  string title = 3; // UTF-8 编码的字符串
  string content = 4; // UTF-8 编码的长字符串
}


message NestedMessage2 {
  Timestamp timestamp = 1012; // 嵌套消息，编号为 1012
  NestedMessage3045 field3045 = 3045; // 嵌套消息，编号为 3045
}

message Timestamp {
  uint64 timestamp_ms = 23; // 整数值 1727337060964
}

message NestedMessage3045 {
  bytes hash = 1; // 字节数据 "\324\267Hl_Vs\324\277..."
  int32 field2 = 2; // 整数值 13

  NestedMessage3045Inner field3 = 3; // 嵌套消息
}

message NestedMessage3045Inner {
  uint64 timestamp = 1; // 整数值 1727337061
  int32 field2 = 2; // 整数值 2
  string app_info = 3; // 字符串 "CAEQ5KTUtwYYlPLt85OAgAMgIw=="

  NestedMostInnerMessage field4 = 4; // 嵌套消息

  uint64 sender = 6; // 整数值 1688855203510548
  int32 field7 = 7; // 整数值 1
}

message NestedMostInnerMessage {
  uint64 field2 = 2; // 整数值 0
}

message AppInfoMes {
  optional uint64 val = 1;
  optional uint64 time = 2;
  optional uint64 userid = 3;
  optional uint32 type = 4;
}