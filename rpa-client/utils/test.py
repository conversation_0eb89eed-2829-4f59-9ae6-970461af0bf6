import message_pb2
import time

# 获取当前时间的秒级时间戳
current_time = time.time()
# 1 分 15 秒前的时间戳（减去 75 秒）
past_time = current_time - 75
# 生成 10 位秒级时间戳
timestamp_10 = int(past_time)
# 生成 13 位毫秒级时间戳
timestamp_13 = int(past_time * 1000)
# 生成 16 位微秒级时间戳
timestamp_16 = int(past_time * 1000000)

# 创建 MainMessage 对象并设置字段
main_message = message_pb2.MainMessage()
main_message.field1 = 1002771
main_message.field2 = 0
main_message.field3 = 0
main_message.sender = 1688855203510548
main_message.receiver = 1688855203510548
main_message.field6 = 0
main_message.field7 = 13

# 设置 BasicCardMessage
main_message.basic_card_message.card_url = "http://qq.com"
main_message.basic_card_message.card_img_url = "https://mobilenew.e-chinalife.com/staticmessage/productResource/icon/<EMAIL>"
main_message.basic_card_message.title = "测试标题"
main_message.basic_card_message.content = "请3天内完成信息确认@@@@@[微笑]"

main_message.field9 = ""
main_message.field11 = 0
main_message.timestamp = timestamp_10
main_message.field13 = 0
main_message.field14 = 65537
main_message.app_info = "CAEQ5KTUtwYYlPLt85OAgAMgIw=="
main_message.field16 = ""

# 设置 NestedMessage2
main_message.field20.timestamp.timestamp_ms = timestamp_13

# 设置 NestedMessage3045
nested_message3045 = main_message.field20.field3045
nested_message3045.hash = b'\xd4\xb7Hl_Vs\xd4\xbf\x84\x38\x42\xa0\xe8\x17\xb4\x85\x1a\xa3\x90\x19\xf6\xb4\x1e\x54\xb3\x63\xd9\xde\xd3x\xeb'
nested_message3045.field2 = 13

# 设置 NestedMessage3045Inner
nested_inner = nested_message3045.field3
nested_inner.timestamp = timestamp_10
nested_inner.field2 = 2
nested_inner.app_info = "CAEQ5KTUtwYYlPLt85OAgAMgIw=="
nested_inner.field4.field2 = 0
nested_inner.sender = 1688855203510548
nested_inner.field7 = 1

main_message.field21 = 0
main_message.field22 = 0

# 序列化为二进制数据
serialized_data = main_message.SerializeToString()

# 输出序列化的二进制数据
serialized_data = serialized_data + b'0x0a'
print(len(serialized_data))
print("22 serialization::archive 12 0 0 1 0 {length} ".format(length=len(serialized_data)).encode() + serialized_data)
boost_ser = ("22 serialization::archive 12 0 0 1 0 {length} ".format(length=len(serialized_data)).encode() + serialized_data).hex()
print(boost_ser)

# 反序列化
new_main_message = message_pb2.MainMessage()
new_main_message.ParseFromString(serialized_data)

# 打印反序列化后的内容
print(new_main_message)
