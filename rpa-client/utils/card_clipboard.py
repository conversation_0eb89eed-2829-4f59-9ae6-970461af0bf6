import win32clipboard as clipboard
import win32con
from PIL import Image
import io
import struct
import win32gui
import win32ui
import os, time, base64
from . import card_pb2
import win32api
from . import print_run_time

@print_run_time
def set_clipboard_data_card(card_title, card_content, card_url, card_img_url):
    # CF_UNICODETEXT (转换 hex 为 Unicode 文本)
    unicode_text_hex = "5B00FE94A5635D000000"
    unicode_text = bytes.fromhex(unicode_text_hex)
    # WeWork Message (转换 hex 为 WeWork 消息)
    # wework_message_hex = "32322073657269616c697a6174696f6e3a3a61726368697665203132203020302031203020333337200894b53d10fdcbdb042094f2edf3938080032894f2edf393808003380d429a010a0d687474703a2f2f71712e636f6d125068747470733a2f2f6d6f62696c656e65772e652d6368696e616c6966652e636f6d2f7374617469636d6573736167652f70726f647563745265736f757263652f69636f6e2f7373792d4032782e706e671a0ce6b58be8af95e6a087e9a2982229e8afb733e5a4a9e58685e5ae8ce68890e4bfa1e681afe7a1aee8aea440404040405be5beaee7ac915d60e5a4d4b706708180047a1c43414551354b5455747759596c504c7438354f4167414d6749773d3da20168a23f08b801e4b4bfeaa232aabe01590a20d4b7486c5f5673d4bf843842a0e817b4851aa39019f6b41e54b363d9ded378eb100d1a3308e5a4d4b70610021a1c43414551354b5455747759596c504c7438354f4167414d6749773d3d22003094f2edf393808003380130783061"
    wework_message_hex = gen_wework_message_hex(card_title, card_content, card_url, card_img_url)
    wework_message = bytes.fromhex(wework_message_hex)
    # HTML Format (转换 hex 为 HTML 数据)
    # html_format_hex = "56657273696F6E3A302E390D0A537461727448544D4C3A303030303030303131370D0A456E6448544D4C3A303030303030303330330D0A5374617274467261676D656E743A303030303030303135330D0A456E64467261676D656E743A303030303030303236370D0A536F7572636555524C3A0D0A3C68746D6C3E0D0A3C626F64793E0D0A3C212D2D5374617274467261676D656E742D2D3E3C6120687265663D2268747470733A2F2F636C7363726D2E652D6368696E616C6966652E636F6D2F636C6270732F637573746F6D2F696E6465782E68746D6C3F636F64653D36704C586C457167594E534D7372764C5A4443314A223EE79086E8B594E68AA5E6A188E794B3E8AFB73C2F613E3C212D2D456E64467261676D656E742D2D3E0D0A3C2F626F64793E0D0A3C2F68746D6C3E"
    # html_format = bytes.fromhex(html_format_hex)
    html_format = gen_html_format(card_title, card_url)
    # 打开剪切板
    clipboard.OpenClipboard()
    # 清空剪切板
    clipboard.EmptyClipboard()
    # 设置 CF_UNICODETEXT 格式的内容
    clipboard.SetClipboardData(win32con.CF_UNICODETEXT, unicode_text.decode('utf-16'))
    # 设置 HTML Format 格式的内容
    html_format_id = clipboard.RegisterClipboardFormat("HTML Format")
    clipboard.SetClipboardData(html_format_id, html_format)
    # 设置 WeWork Message 格式的内容
    wework_format_id = clipboard.RegisterClipboardFormat("WeWork Message")
    clipboard.SetClipboardData(wework_format_id, wework_message)
    # 关闭剪切板
    clipboard.CloseClipboard()

def gen_wework_message_hex(card_title, card_content, card_url, card_img_url):
    # 获取当前时间的秒级时间戳
    current_time = time.time()
    # 1 分 15 秒前的时间戳（减去 75 秒）
    past_time = current_time - 75
    # 生成 10 位秒级时间戳
    timestamp_10 = int(past_time)
    # 生成 13 位毫秒级时间戳
    timestamp_13 = int(past_time * 1000)
    # 生成 16 位微秒级时间戳
    timestamp_16 = int(past_time * 1000000)
    app_info_mes = card_pb2.AppInfoMes()
    app_info_mes.val = 1
    app_info_mes.time = int(current_time)
    app_info_mes.userid = 1688855203510547
    app_info_mes.type = 65
    app_info = base64.b64encode(app_info_mes.SerializeToString())
    # 创建 MainMessage 对象并设置字段
    main_message = card_pb2.MainMessage()
    main_message.field1 = 1002771
    main_message.field2 = 0
    main_message.field3 = 0
    main_message.sender = 1688855203510547
    main_message.receiver = 1688855203510547
    main_message.field6 = 0
    main_message.field7 = 13
    # 设置 BasicCardMessage
    main_message.basic_card_message.card_url = card_url
    main_message.basic_card_message.card_img_url = card_img_url
    main_message.basic_card_message.title = card_title
    main_message.basic_card_message.content = card_content
    main_message.field9 = ""
    main_message.field11 = 0
    main_message.timestamp = timestamp_10
    main_message.field13 = 0
    main_message.field14 = 65537
    main_message.app_info = app_info
    main_message.field16 = ""
    # 设置 NestedMessage2
    main_message.field20.timestamp.timestamp_ms = timestamp_13
    # 设置 NestedMessage3045
    nested_message3045 = main_message.field20.field3045
    nested_message3045.hash = b'\xd4\xb7Hl_Vs\xd4\xbf\x84\x36\x42\xa0\xe8\x17\xb4\x85\x1a\xa3\x90\x19\xf6\xb4\x1e\x54\xb3\x63\xd9\xde\xd3x\xeb'
    nested_message3045.field2 = 13
    # 设置 NestedMessage3045Inner
    nested_inner = nested_message3045.field3
    nested_inner.timestamp = timestamp_10
    nested_inner.field2 = 2
    nested_inner.app_info = app_info
    nested_inner.field4.field2 = 0
    nested_inner.sender = 1688855203510547
    nested_inner.field7 = 1
    main_message.field21 = 0
    main_message.field22 = 0
    # 序列化为二进制数据
    serialized_data = main_message.SerializeToString()
    # 输出序列化的二进制数据
    serialized_data = serialized_data + b'0x0a'
    # logging.info(base64.b64encode(serialized_data))
    # print(
    #     "22 serialization::archive 12 0 0 1 0 {length} ".format(
    #         length=len(serialized_data)).encode() + serialized_data)
    boost_ser = ("22 serialization::archive 12 0 0 1 0 {length} ".format(
        length=len(serialized_data)).encode() + serialized_data).hex()
    # print(boost_ser)
    return boost_ser


def gen_html_format(card_title, card_url):
    url = card_url.encode()
    url_content = card_title.encode()
    html_content = b"""<html>\r\n<body>\r\n<!--StartFragment--><a href="%s">%s</a><!--EndFragment-->\r\n</body>\r\n</html>""" % (
        url, url_content)
    meta_content = b"""Version:0.9\r\nStartHTML:0000000117\r\nEndHTML:0000000425\r\nStartFragment:0000000153\r\nEndFragment:0000000389\r\nSourceURL:\r\n"""
    template_content = meta_content + html_content
    version = "0.9"
    start_html = f"{len(meta_content):0>10}"
    end_html = f"{len(template_content):0>10}"
    start_fragment = f"{template_content.find(b'<!--StartFragment-->') + len(b'<!--StartFragment-->'):0>10}"
    end_fragment = f"{template_content.find(b'<!--EndFragment-->'):0>10}"
    clipboard_format = f"""Version:{version}\r\nStartHTML:{start_html}\r\nEndHTML:{end_html}\r\nStartFragment:{start_fragment}\r\nEndFragment:{end_fragment}\r\nSourceURL:\r\n""".encode() + html_content
    return clipboard_format