# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: utils/file.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10utils/file.proto\"\xc1\x02\n\x0b\x46ileMessage\x12\x0e\n\x06\x66ield1\x18\x01 \x01(\x05\x12\x0e\n\x06\x66ield2\x18\x02 \x01(\x05\x12\x0e\n\x06\x66ield3\x18\x03 \x01(\x05\x12\x0e\n\x06\x66ield4\x18\x04 \x01(\x05\x12\x0e\n\x06\x66ield5\x18\x05 \x01(\x05\x12\x0e\n\x06\x66ield6\x18\x06 \x01(\x05\x12\x0e\n\x06\x66ield7\x18\x07 \x01(\x05\x12\x19\n\x06\x66ield8\x18\x08 \x01(\x0b\x32\t.FileInfo\x12\x0e\n\x06\x66ield9\x18\t \x01(\t\x12\x0f\n\x07\x66ield11\x18\x0b \x01(\x05\x12\x0f\n\x07\x66ield12\x18\x0c \x01(\x05\x12\x0f\n\x07\x66ield13\x18\r \x01(\x05\x12\x0f\n\x07\x66ield14\x18\x0e \x01(\x05\x12\x0f\n\x07\x66ield15\x18\x0f \x01(\x0c\x12\x0f\n\x07\x66ield16\x18\x10 \x01(\x0c\x12\x0f\n\x07\x66ield20\x18\x14 \x01(\x0c\x12\x0f\n\x07\x66ield21\x18\x15 \x01(\x05\x12\x0f\n\x07\x66ield22\x18\x16 \x01(\x05\"<\n\x08\x46ileInfo\x12\x0e\n\x06\x66ield2\x18\x02 \x01(\t\x12\x0e\n\x06\x66ield4\x18\x04 \x01(\x05\x12\x10\n\x08\x66ield100\x18\x64 \x01(\tb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'utils.file_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _FILEMESSAGE._serialized_start=21
  _FILEMESSAGE._serialized_end=342
  _FILEINFO._serialized_start=344
  _FILEINFO._serialized_end=404
# @@protoc_insertion_point(module_scope)
