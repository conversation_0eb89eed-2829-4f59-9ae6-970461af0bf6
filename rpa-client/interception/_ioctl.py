# Constants for the CTL_CODE macro
# See: https://github.com/tpn/winsdk-10/blob/master/Include/10.0.16299.0/km/d4drvif.h
IOCTL_DOT4_USER_BASE = 2049
FILE_DEVICE_UNKNOWN = 0x00000022
METHOD_BUFFERED = 0
FILE_ANY_ACCESS = 0


# Python equivalent of https://github.com/tpn/winsdk-10/blob/9b69fd26ac0c7d0b83d378dba01080e93349c2ed/Include/10.0.16299.0/km/d4drvif.h#L38
def ctl(device_type, function_code, method, access):
    return (device_type << 16) | (access << 14) | (function_code << 2) | method


# Create the IOCTL codes that we need to use
IOCTL_SET_PRECEDENCE = ctl(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
IOCTL_GET_PRECEDENCE = ctl(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)
IOCTL_SET_FILTER = ctl(FILE_DEVICE_UNKNOWN, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)
IOCTL_GET_FILTER = ctl(FILE_DEVICE_UNKNOWN, 0x808, METHOD_BUFFERED, FILE_ANY_ACCESS)
IOCTL_SET_EVENT = ctl(FILE_DEVICE_UNKNOWN, 0x810, METHOD_BUFFERED, FILE_ANY_ACCESS)
IOCTL_WRITE = ctl(FILE_DEVICE_UNKNOWN, 0x820, METHOD_BUFFERED, FILE_ANY_ACCESS)
IOCTL_READ = ctl(FILE_DEVICE_UNKNOWN, 0x840, METHOD_BUFFERED, FILE_ANY_ACCESS)
IOCTL_GET_HARDWARE_ID = ctl(
    FILE_DEVICE_UNKNOWN, 0x880, METHOD_BUFFERED, FILE_ANY_ACCESS
)
