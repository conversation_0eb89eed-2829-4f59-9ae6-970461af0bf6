import json
import os
import random
import signal
import time
import uuid
import winreg
from abc import ABC
from datetime import datetime
from urllib.parse import quote
from urllib.request import urlopen
import logging
import psutil
import pyperclip
import win32api
import win32clipboard
import win32con
import win32gui
import win32process
import ctypes
import os
import socket
import getpass
from PIL import ImageGrab
from pywinauto.application import Application
from pywinauto.controls.hwndwrapper import HwndWrapper
from pywinauto.findwindows import find_elements
from win32.win32api import GetSystemMetrics

import airtest.core.api
import airtest.core.win
from airtest.core.cv import Template
from handler import handler
from pywinauto.win32functions import windll
from config import RPAVERSION


class ClientStatus:
    OFFLINE = 0
    ONLINE = 1
    WAITING = 2
    ERROR = 3
    SUSPENDED = 4


class TaskStatus:
    CREATED = 0
    RUNNING = 1
    DELETED = 2
    CANCELLED = 3
    FINISHED = 10
    FAILED = 11
    TIMEOUT = 12
    OFFLINE = 13
    TERMINATED = 14
    UNSUPPORTED = 15


class MessageType:
    TEXT = 'text'
    IMAGE = 'image'
    VIDEO = 'video'
    FILE = 'file'
    CARD = 'link'
    MINIPROGRAM = 'weapp'
    MENTION = 'mention'


class AppConfig:
    def __init__(self, app_name, login_class_name, main_class_name, process_name, mutex_names, registry_path,
                 registry_key):
        self.app_name = app_name
        self.login_class_name = login_class_name
        self.main_class_name = main_class_name
        self.process_name = process_name
        self.mutex_names = mutex_names
        self.registry_path = registry_path
        self.registry_key = registry_key

    def __str__(self) -> str:
        return f"app_name: {self.app_name}, " \
               f"login_class_name: {self.login_class_name}, " \
               f"main_class_name: {self.main_class_name}, " \
               f"process_name: {self.process_name}, " \
               f"mutex_names: {self.mutex_names}, " \
               f"registry_path: {self.registry_path}, " \
               f"registry_key: {self.registry_key}"


class AppUser:
    def __init__(self, account=None, nickname=None, realname=None, company=None, phone=None, avatar=None):
        self.account = account
        self.nickname = nickname
        self.realname = realname
        self.company = company
        self.phone = phone
        self.avatar = avatar

    def __str__(self) -> str:
        return f"account: {self.account}, " \
               f"nickname: {self.nickname}, " \
               f"realname: {self.realname}, " \
               f"company: {self.company}, " \
               f"phone: {self.phone}, " \
               f"avatar: {self.avatar}"


class App(ABC):
    def __init__(self, handle: int, process: int = None, status: int = None, user: AppUser = None):
        self.handle = handle
        self.process = process
        self.status = status
        self.user = user
        self.root_path = os.path.dirname(__file__)
        self.assets_path = os.path.abspath(os.path.join(self.root_path, '../assets/'))
        self.temps_path = os.path.abspath(os.path.join(self.root_path, '../temps/'))
        self.started_time = self.now()
        self.online_time = None
        self.pos_cache = {}
        self.last_send = ''
        self.need_refresh = False
        self.screen_width = win32api.GetSystemMetrics(0)
        self.screen_height = win32api.GetSystemMetrics(1)
        self.scale = ctypes.windll.user32.GetDpiForSystem() / 96.0
        self.rpa_version = RPAVERSION
        self.pc_user = self.get_system_user()
        self.ip = self.get_local_ip()

    @staticmethod
    def config() -> AppConfig:
        raise NotImplementedError

    @classmethod
    def app_id(cls):
        return cls.config().app_name

    @classmethod
    def is_login_window(cls, name: str):
        return cls.config().login_class_name == name

    @classmethod
    def is_main_window(cls, name: str):
        return cls.config().main_class_name == name

    @classmethod
    def find_elements(cls, process=None):
        class_name_re = f'^{cls.config().login_class_name}$|^{cls.config().main_class_name}$'
        return find_elements(class_name_re=class_name_re, process=process)

    @classmethod
    def launch(cls, number: int = 1, default_path=None):
        config = cls.config()
        app_path = None
        if default_path and os.path.exists(default_path):
            # Use custom path
            app_path = default_path
        elif config.registry_path and config.registry_key:
            # Find path from registry
            sub_key = winreg.OpenKeyEx(winreg.HKEY_LOCAL_MACHINE, config.registry_path)
            values = winreg.QueryValueEx(sub_key, config.registry_key)
            if values and values[0]:
                tmp = values[0].strip()
                if tmp.startswith('\"') or tmp.startswith('\''):
                    tmp = tmp[1:]
                if tmp.endswith('\"') or tmp.endswith('\''):
                    tmp = tmp[:-1]
                app_path = tmp
        if not app_path:
            raise Exception(f"Cannot find '{cls.app_id()}' path")

        # Close mutexes before launching
        processes = []
        for i in range(number):
            if config.process_name and config.mutex_names:
                process_ids = []
                for proc in psutil.process_iter(attrs=['name']):
                    if proc.info['name'] == config.process_name:
                        process_ids.append(proc.pid)
                if process_ids:
                    handles = handler.find_handles(process_ids=process_ids, handle_names=config.mutex_names)
                    handler.close_handles(handles)
            p = win32process.CreateProcess(app_path, '', None, None, 0, 0, None, None, win32process.STARTUPINFO())
            if p and len(p) == 4:
                processes.append(p[2])
        return processes

    def find_userinfo(self) -> AppUser:
        raise NotImplementedError

    def execute(self, task_type, task_data=None):
        func = getattr(self, task_type)
        if not func:
            raise NotImplementedError
        return func(task_data)

    def init(self):
        def load_cache():
            title = win32gui.GetWindowText(self.handle)
            print("title:", title)
            if not title or not title.startswith(self.app_id() + ' '):
                return False
            cache = json.loads(title[len(self.app_id()) + 1:])
            user = AppUser()
            for attr in user.__dict__:
                user.__dict__[attr] = cache.get(attr)
            self.user = user
            self.started_time = cache.get('st')
            self.online_time = cache.get('ot')
            cache['version'] = self.rpa_version
            title = json.dumps(cache, separators=(',', ':'))
            win32gui.SetWindowText(self.handle, self.app_id() + ' ' + title)

            return user

        def set_cache():
            u = {k: v for k, v in self.user.__dict__.items() if v is not None}
            if self.started_time:
                u['st'] = self.started_time
            if self.online_time:
                u['ot'] = self.online_time
            if self.rpa_version:
                u['version'] = self.rpa_version
            title = json.dumps(u, separators=(',', ':'))
            win32gui.SetWindowText(self.handle, self.app_id() + ' ' + title)

        try:
            if load_cache():
                return True
            user = self.find_userinfo()
            if user:
                self.user = user
                self.online_time = self.now()
                set_cache()
                return True
            return False
        except:
            logging.exception('Failed to find user info')
        return False

    def now(self):
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 获取系统用户名（增强兼容性）
    def get_system_user(self):
        for var in ['LOGNAME', 'USER', 'LNAME', 'USERNAME']:
            user = os.environ.get(var)
            if user:
                print(user)
                return user
        try:
            print(user)
            return getpass.getuser()
        except:
            print("未知用户")
            return "未知用户"

    # 获取本地IP地址（局域网）
    def get_local_ip(self):
        try:
            # 创建一个UDP连接（不会实际发送数据）
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))  # 连接Google DNS
                print(s.getsockname()[0])
                return s.getsockname()[0]
        except Exception as e:
            # 回退方案
            hostname = socket.gethostname()
            print(hostname)
            return socket.gethostbyname(hostname)

    def __str__(self) -> str:
        return f"handle: {self.handle}, " \
               f"process: {self.process}, " \
               f"status: {self.status}, " \
               f"user: {str(self.user)}, " \
               f"started_time: {self.started_time}, " \
               f"online_time: {self.online_time}"

    @classmethod
    def print_run_time(cls, func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            # 获取函数名
            func_name = func.__name__
            # 获取参数名及参数值
            arg_names = func.__code__.co_varnames[:func.__code__.co_argcount]
            # 排除 self 参数
            params = ', '.join(
                f"{name}={value!r}" for name, value in zip(arg_names, args) if name != 'self'
            )
            params += ''.join(f", {k}={v!r}" for k, v in kwargs.items())
            # 调用实际函数
            ret = func(*args, **kwargs)
            # 计算运行时间

            elapsed_time = time.time() - start_time
            logging.info(f"调用 {func_name}({params}) 耗时 {elapsed_time:.4f} 秒")
            # 如果返回值是字典，添加运行时间
            if ret and isinstance(ret, dict):
                ret["time"] = elapsed_time
            return ret

        return wrapper


class Capable:
    def close_handle(self, handle):
        if not handle:
            return
        hwnd = HwndWrapper(handle)
        hwnd.close()

    def kill(self, process):
        if not process:
            return
        os.kill(process, signal.SIGABRT)

    @App.print_run_time
    def snapshot(self, handle):
        win32gui.SendMessage(handle, win32con.WM_SYSCOMMAND, win32con.SC_RESTORE, 0)
        win32gui.SetForegroundWindow(handle)
        return ImageGrab.grab((win32gui.GetWindowRect(handle)))

    @App.print_run_time
    def download(self, urls, dir_path):
        if not urls or not dir_path:
            return {}
        try:
            os.makedirs(dir_path, exist_ok=True)
            paths = {}
            # for url in urls:
            for url_info in urls:
                url = list(url_info.keys())[0]
                original_filename = url_info.get(url)
                # original_filename = os.path.basename(url)
                path = os.path.join(dir_path, original_filename)
                base, ext = os.path.splitext(original_filename)

                counter = 1
                while os.path.exists(path):
                    path = os.path.join(dir_path, f"{base}({counter}){ext}")
                    counter += 1

                # outurl = url
                # if 'http://nrybj.clic:8087' in url:
                #     outurl = url.replace('http://nrybj.clic:8087', 'https://nrybj.e-chinalife.com')

                inurl = url
                if 'https://nrybj.e-chinalife.com' in url:
                    inurl = url.replace('https://nrybj.e-chinalife.com', 'http://nrybj.clic:8087')

                logging.info('Downloading the file, url: %s, path: %s', url, path)
                response = urlopen(quote(inurl, safe='/:?='))

                if response.getcode() == 200:
                    with open(path, "wb") as f:
                        f.write(response.read())
                        paths[url] = f.name
                else:
                    logging.error('Failed to download the file, url: %s', url)
            return paths

        except Exception as e:
            logging.exception('Failed to download files')
            raise Exception(f"Failed to download files, urls: {urls}, error: {str(e)}")

    @App.print_run_time
    def copy(self, v):
        # pyperclip.copy(v)
        win32clipboard.OpenClipboard()
        win32clipboard.EmptyClipboard()
        win32clipboard.SetClipboardText(v, win32con.CF_UNICODETEXT)
        win32clipboard.CloseClipboard()

    @App.print_run_time
    def paste(self):
        # return pyperclip.paste()
        win32clipboard.OpenClipboard()
        data = win32clipboard.GetClipboardData()
        win32clipboard.CloseClipboard()
        return data

    def wait(self, seconds=0):
        if seconds > 0:
            time.sleep(seconds)


class UiaApp(App, Capable):
    def __init__(self, handle: int, process: int = None, status: int = None, user=None):
        super().__init__(handle, process, status, user)

    def connect(self, handle=None, foreground=True, backend='uia'):
        if not handle:
            handle = self.handle
        if foreground:
            window = HwndWrapper(handle)
            window.set_focus()
            window.move_window(0, 0)
        app = Application(backend=backend)
        app.connect(handle=handle)
        return app


class AirApp(App, Capable):
    def __init__(self, handle: int, process: int = None, status: int = None, user=None):
        super().__init__(handle, process, status, user)

    @App.print_run_time
    def connect(self, handle=None, foreground=True, changeSize=True):
        if not handle:
            handle = self.handle
        device = airtest.core.api.connect_device("Windows:///" + str(handle))
        if foreground:
            device.set_foreground()
            device.move((0, 0), changeSize)
            logging.info(device.get_rect())
        return device

    @App.print_run_time
    def exists(self, v, need_cv=False):
        if need_cv:
            if isinstance(v, Template):
                return airtest.core.api.exists(v)
            elif isinstance(v, str):
                return airtest.core.api.exists(Template(os.path.join(self.assets_path, self.app_id(), v)))
            elif isinstance(v, tuple):
                return v
            elif isinstance(v, list):
                for i in v:
                    r = self.exists(i, need_cv)
                    if r:
                        return r
            else:
                return None
        else:
            if isinstance(v, Template):
                if not self.need_refresh and v.filepath in self.pos_cache and self.pos_cache[v.filepath] is not None:
                    return self.pos_cache[v.filepath]
                else:
                    pos = airtest.core.api.exists(v)
                    if pos:
                        self.pos_cache[v.filepath] = pos
                    return pos
            elif isinstance(v, str):
                if not self.need_refresh and v in self.pos_cache and self.pos_cache[v] is not None:
                    return self.pos_cache[v]
                else:
                    pos = airtest.core.api.exists(Template(os.path.join(self.assets_path, self.app_id(), v)))
                    if pos:
                        self.pos_cache[v] = pos
                    return pos
            elif isinstance(v, tuple):
                return v
            elif isinstance(v, list):
                if not self.need_refresh and tuple(v) in self.pos_cache and self.pos_cache[tuple(v)] is not None:
                    return self.pos_cache[tuple(v)]
                else:
                    for i in v:
                        r = self.exists(i)
                        if r:
                            self.pos_cache[tuple(v)] = r
                            return r
            else:
                return None

    @App.print_run_time
    def click(self, v, wait_time=0):
        ok = self.exists(v)
        if not ok:
            raise Exception("Failed to find to click")
        if ok:
            start_time = time.time()
            # airtest.core.api.touch(ok)
            windll.user32.SetCursorPos(ok[0], ok[1])
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            # time.sleep(0.02)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            end_time = time.time()
            elapsed_time = end_time - start_time
            print(f"单纯点击操作花费的时间为 {elapsed_time} 秒")
            self.wait(wait_time)
        return ok

    @App.print_run_time
    def double_click(self, v, wait_time=0):
        ok = self.exists(v)
        if ok:
            airtest.core.api.double_click(ok)
            self.wait(wait_time)
            return ok
        else:
            return False

    @App.print_run_time
    def key(self, key, wait_time=0):
        airtest.core.api.keyevent(key, pause=0.01)
        self.wait(wait_time)

    def random_text(self, wait_time=0):
        def press_key(key_code, shift=False):
            """模拟按下并释放一个键，可选是否使用Shift键"""
            # 如果需要Shift键（用于大写字母）
            if shift:
                win32api.keybd_event(win32con.VK_SHIFT, 0, 0, 0)
                time.sleep(0.05)

            # 按下键
            win32api.keybd_event(key_code, 0, 0, 0)
            time.sleep(0.05)
            # 释放键
            win32api.keybd_event(key_code, 0, win32con.KEYEVENTF_KEYUP, 0)
            time.sleep(0.05)

            # 释放Shift键（如果使用了）
            if shift:
                win32api.keybd_event(win32con.VK_SHIFT, 0, win32con.KEYEVENTF_KEYUP, 0)
                time.sleep(0.05)

        # 字母表（包括大写）
        letters = 'abcdefghijklmnopqrstuvwxyz'
        # 随机选择3个字母（可以重复也可以不重复）
        selected_letters = random.choices(letters, k=3)
        # 创建虚拟键码映射（A-Z键码）
        # ASCII 'a'是97，win32con.VK_A是65（代表大写A）
        # 所以字母键码范围是65-90（A-Z）
        for letter in selected_letters:
            # 决定是否大写（随机，但不影响字母本身选择）
            # 这样可以有大小写混合效果
            is_capital = random.choice([True, False])
            # 获取键码（所有字母键码就是大写字母的ASCII）
            key_code = ord(letter.upper())
            # 输入字母
            press_key(key_code, shift=is_capital)
            # 字母间加入随机延迟（20-100毫秒）
            time.sleep(random.uniform(0.02, 0.1))

        self.wait(wait_time)


    def get_screen_scale_ratio(self):
        logging.debug("ratio:{%s}", self.scale / 2)
        return self.scale / 2

    def get_foreground_window_name(self):
        foreground_window_handle = win32gui.GetForegroundWindow()
        app = self.connect(foreground_window_handle)
        foreground_window_class = app.get_class_name()
        return foreground_window_class

    def move(self, x, y, wait_time=0):
        win32api.mouse_event(win32con.MOUSEEVENTF_MOVE, x, y, 0, 0)
        time.sleep(wait_time)

    def swing_mouse(self, duration_sec=0.5, step_pixels=20, interval_sec=0.1, direction=1):
        """
        控制鼠标左右摆动生成轨迹
        :param duration_sec: 总持续时间（秒）
        :param step_pixels: 每次移动的像素步长
        :param interval_sec: 每次移动的时间间隔
        """
        steps = int(duration_sec / interval_sec)

        for _ in range(steps):
            # 发送鼠标移动事件（禁用合并）
            win32api.mouse_event(
                win32con.MOUSEEVENTF_MOVE | win32con.MOUSEEVENTF_MOVE_NOCOALESCE,
                step_pixels * direction,  # X方向增量
                0  # Y方向不变
            )
            time.sleep(interval_sec)

    def move_mouse(self, x, y, wait_time=0):
        # 发送鼠标移动事件（禁用合并）
        win32api.mouse_event(
            win32con.MOUSEEVENTF_MOVE | win32con.MOUSEEVENTF_MOVE_NOCOALESCE,
            x,  # X方向
            y  # Y方向
        )
        time.sleep(wait_time)