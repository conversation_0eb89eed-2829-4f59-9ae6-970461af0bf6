import json
import logging
import random
import time
from datetime import datetime
import win32clipboard as clipboard
import win32con
import win32gui
from PIL import ImageGrab
from pywinauto.findwindows import find_elements, find_window, ElementNotFoundError
from pyzbar import pyzbar
import base64
import io
import sys
import interception
from pywinauto import WindowNotFoundError
sys.path.append("../")
from .app import AirApp, AppConfig, AppUser, MessageType
from utils.image_clipboard import set_clipboard_data_image
from utils.file_clipboard import set_clipboard_data_file
from utils.card_clipboard import set_clipboard_data_card
from utils.miniprogram_clipboard import set_clipboard_data_miniprogram

class WeCom(AirApp):
    def __init__(self, handle: int, process: int = None, status: int = None, user=None):
        super().__init__(handle, process, status, user)
        self.version = "4.1.26.6014"

    @staticmethod
    def config():
        return AppConfig(
            app_name="wecom",
            login_class_name="WeChatLogin",
            main_class_name="WeWorkWindow",
            process_name="WXWork.exe",
            mutex_names=[r"\Tencent.WeWork.ExclusiveObject", r"\Tencent.WeWork.ExclusiveObjectInstance1"],
            registry_path=r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\企业微信",
            registry_key="DisplayIcon")

    @AirApp.print_run_time
    def find_userinfo(self):
        config_handle = None
        edit_handle = None
        try:
            interception.auto_capture_devices(keyboard=True, mouse=True, verbose=True)
            # wait until ConfirmFinancialCorpVersionWindow appear
            time.sleep(1.5)
            # Connect to ConfirmFinancialCorpVersionWindow
            try:
                conversation_archive_confirm_handle = find_window(class_name='ConfirmFinancialCorpVersionWindow',
                                                                  process=self.process)
                if conversation_archive_confirm_handle:
                    self.connect(conversation_archive_confirm_handle)
                    # click the conversation archive button if exists
                    confirm_pos = self.exists('main/conversation_archive_confirm.png')
                    if confirm_pos:
                        interception.click(confirm_pos[0], confirm_pos[1])
            except:
                print("there is no ConfirmFinancialCorpVersionWindow")

            self.connect(self.handle)
            # Popup userinfo menu
            self._popup_userinfo()

            time.sleep(1)
            # Click the setting button
            setting_button_pos = self.exists(
                ['userinfo_popup/setting_button1080.jpg', 'userinfo_popup/setting_button.png',
                 'userinfo_popup/setting_button_low.png',
                 'userinfo_popup/setting_button_qiyu.jpg'], True)
            if not setting_button_pos:
                raise Exception("Unable to get setting button")
            time.sleep(1)
            setting_click_x = int(setting_button_pos[0] + 100 * self.get_screen_scale_ratio())
            setting_click_y = setting_button_pos[1]
            interception.click(setting_click_x, setting_click_y)
            time.sleep(0.5)

            # Connect to setting window
            try:
                config_handle = find_window(class_name='ConfigWindow', process=self.process)
            except WindowNotFoundError:
                # 等待一秒再次点击一次
                time.sleep(2)
                self._popup_userinfo()
                setting_button_pos = self.exists(
                    ['userinfo_popup/setting_button1080.jpg', 'userinfo_popup/setting_button.png',
                     'userinfo_popup/setting_button_low.png',
                     'userinfo_popup/setting_button_qiyu.jpg'], True)
                if setting_button_pos:
                    setting_click_x = int(setting_button_pos[0] + 100 * self.get_screen_scale_ratio())
                    setting_click_y = setting_button_pos[1]
                    interception.click(setting_click_x, setting_click_y)
                    time.sleep(0.5)
                config_handle = find_window(class_name='ConfigWindow', process=self.process)

            if not config_handle:
                return None
            self.connect(config_handle)

            # Click userinfo button
            userinfo_tab_pos = self.exists(
                ['setting/userinfo_tab1080.jpg', 'setting/userinfo_tab.png', 'setting/userinfo_tab_low.jpg',
                 'setting/userinfo_tab_focus.png', 'setting/userinfo_tab_focus_low.jpg', ])
            if not userinfo_tab_pos:
                return None
            interception.click(userinfo_tab_pos[0], userinfo_tab_pos[1])
            time.sleep(2)

            # Find company name
            company_pos_x = userinfo_tab_pos[0] + int(463 * self.get_screen_scale_ratio())
            company_pos_y = userinfo_tab_pos[1] + int(35 * self.get_screen_scale_ratio())
            interception.click(company_pos_x, company_pos_y, clicks=2)
            time.sleep(2)
            self.copy("")
            time.sleep(0.5)
            with interception.hold_key("ctrl"):
                interception.press("c")
            time.sleep(0.1)
            company = self.paste()
            print(company)
            if company is None:
                logging.info(f'登录未成功，公司名未获取')
                return None

            # Click edit userinfo button
            edit_btn_pos = self.exists(['setting/userinfo_edit_btn1080.jpg', 'setting/userinfo_edit_btn.png',
                                        'setting/userinfo_edit_btn_low.png'])
            if edit_btn_pos:
                interception.click(edit_btn_pos[0], edit_btn_pos[1])
            time.sleep(1)

            # Connect to edit userinfo window
            edit_handle = find_window(class_name='ModifyUserInfoWindow', process=self.process)
            if not edit_handle:
                return None
            self.connect(edit_handle, foreground=True)
            time.sleep(2)

            # Find nickname
            label_name_pos = self.exists(['setting/userinfo_name_label1080.jpg', 'setting/userinfo_name_label.png',
                                          'setting/userinfo_name_label_low.jpg'])
            if not label_name_pos:
                return None
            time.sleep(0.5)
            nickname_pos_x = int(label_name_pos[0] + 110 * self.get_screen_scale_ratio())
            nickname_pos_y = label_name_pos[1] + 0
            interception.click(nickname_pos_x, nickname_pos_y, clicks=2)
            time.sleep(1)
            self.copy("")
            with interception.hold_key("ctrl"):
                interception.press("a")
                time.sleep(0.05)
                interception.press("c")
            time.sleep(0.1)
            nickname = self.paste()
            print(nickname)
            if nickname is None:
                logging.info(f'登录未成功，用户名未获取')
                return None
            # TODO WeCom account is set to '${company}_${nickname}'
            logging.info(f'当前登录用户是：{company}_{nickname}')
            return AppUser(account=company + '_' + nickname, nickname=nickname, company=company)
        finally:
            self.close_handle(config_handle)
            self.close_handle(edit_handle)

    @AirApp.print_run_time
    def login(self, data=None):
        logging.debug('Login, handle: %d', self.handle)
        self.connect(self.handle)

        # 1. Try to refresh the QR code
        # 2. Try to switch account
        # 3. Try to clear last login info
        if self.exists(['login/refresh_qrcode_btn_new.png', 'login/refresh_qrcode_btn.png',
                        'login/switch_account_btn.png',
                        'login/back_btn_new.png', 'login/back_btn.png', 'login/network_refresh.png']):
            self.click(['login/refresh_qrcode_btn_new.png', 'login/refresh_qrcode_btn.png',
                        'login/switch_account_btn.png',
                        'login/back_btn_new.png', 'login/back_btn.png', 'login/network_refresh.png'], 1)

        # Read the QR code and decode it to URL
        snapshot = self.snapshot(self.handle)
        decoded = pyzbar.decode(snapshot)
        if decoded and decoded[0]:
            return {'qrcode': decoded[0].data.decode('utf-8')}
        raise Exception("Unable to get QR code")

    @AirApp.print_run_time
    def logout(self, data=None):
        logging.debug('Logout, handle: %d', self.handle)
        # Kill the process
        self.kill(self.process)

    def check_pop_up_and_close(self):
        try:
            message_box_handle = find_window(class_name='WeWorkMessageBoxFrame')
            if message_box_handle:
                self.connect(message_box_handle)
                pos = self.exists('main/sensitive_word_blocker.png')
                if pos:
                    self.click(pos)
                self.connect(self.handle, foreground=True)
        except Exception as e:
            print("未弹出新窗口：", e)

    def move_side_to_side(self, data=None):
        self.swing_mouse(0.25, 10, 0.025, 1)
        self.swing_mouse(0.25, 10, 0.025, -1)

    def get_login_image(self, data=None):
        try:
            # 登陆页面
            double_verify_class_name = 'WeChatLogin'
            hwnd = find_window(class_name=double_verify_class_name)
            if hwnd:
                self.connect(hwnd, changeSize=False)
                # 点击刷新的位置
                refresh_pos = self.exists(['login/refresh.png', 'login/refresh_1080.png'])
                if refresh_pos:
                    self.click(refresh_pos)
                rect = win32gui.GetWindowRect(hwnd)
                screenshot = ImageGrab.grab(rect)  # 返回PIL.Image对象
                buf = io.BytesIO()
                screenshot.save(buf, format='PNG')
                img_bytes = buf.getvalue()  # 这里就是要发送的二进制数据
                img_b64 = base64.b64encode(img_bytes).decode('ascii')  # 图片转base64字符串
                logging.info(f"Base64大小: {len(img_b64) / 1024:.2f} KB")
                return img_b64
        except WindowNotFoundError as e:
            time.sleep(5)
            logging.debug('无需处理')

    @AirApp.print_run_time
    def send_private_messages(self, data):
        search_tab_wnd = None
        try:
            interception.auto_capture_devices(keyboard=True, mouse=True, verbose=True)

            logging.debug('Send private messages, handle: %d, data: %s', self.handle, data)

            # Validate data before sending messages
            target, messages, file_paths = self._check_messages(data)

            self.connect(self.handle, foreground=True)

            if target != self.last_send:
                # 打开全局搜索 Ctrl+Alt+F
                with interception.hold_key("ctrl"):
                    with interception.hold_key("alt"):
                        interception.press("f")
                time.sleep(0.2)

                try:
                    search_tab_wnd = find_window(class_name_re='SearchTabWnd')
                except WindowNotFoundError:
                    search_tab_wnd = None

                if not search_tab_wnd:
                    cnt = 0
                    while cnt < 5:
                        time.sleep(1)
                        try:
                            search_tab_wnd = find_window(class_name_re='SearchTabWnd')
                        except WindowNotFoundError:
                            search_tab_wnd = None
                        if search_tab_wnd:
                            self.connect(search_tab_wnd)
                            break
                        cnt += 1

                    if not search_tab_wnd:
                        self.need_refresh = True
                        raise Exception("Failed to open search window")
                else:
                    # 连接全局搜索
                    self.connect(search_tab_wnd)
                # 点击联系人
                contact_pos = self.exists(
                    ['group_chat/contact1080.jpg', 'group_chat/contact.png', 'group_chat/contact_low.png',
                     'group_chat/contact_low_1.png'])
                if not contact_pos:
                    self.need_refresh = True
                    self.close_handle(search_tab_wnd)
                    raise Exception("Failed to find contact tab")
                interception.click(contact_pos[0], contact_pos[1])
                time.sleep(0.1)

                # 搜索
                search_input_x = int(contact_pos[0])
                search_input_y = int(contact_pos[1] - 90 * self.get_screen_scale_ratio())
                interception.click(search_input_x, search_input_y)
                time.sleep(0.1)

                self.copy(target)
                with interception.hold_key("ctrl"):
                    interception.press("a")
                    time.sleep(0.05)
                    interception.press("v")
                time.sleep(0.5)

                window_name = self.get_foreground_window_name()
                print(f"window name: {window_name}")
                user_search_start_time = time.time()
                while (window_name == 'SearchTabWnd2' or window_name == 'SearchTabWnd'):
                    click_x = int(contact_pos[0] + 450 * self.get_screen_scale_ratio())
                    click_y = int(contact_pos[1] + 160 * self.get_screen_scale_ratio())
                    interception.click(click_x, click_y)
                    time.sleep(0.2)
                    
                    window_name = self.get_foreground_window_name()
                    print(f"foregroud_name:{window_name}")
                    if window_name != 'SearchTabWnd2' and window_name != 'SearchTabWnd':
                        break

                    # double check
                    count = 0
                    while count < 5:
                        count = count + 1
                        if window_name == 'SearchTabWnd2' or window_name == 'SearchTabWnd':
                            window_name = self.get_foreground_window_name()
                            print(f"double check foregroud_name:{window_name}")

                        if window_name != 'SearchTabWnd2' and window_name != 'SearchTabWnd':
                            break
                        time.sleep(0.1)
                    if window_name != 'SearchTabWnd2' and window_name != 'SearchTabWnd':
                        break

                    no_result = self.exists(['group_chat/no_result_to_find1080.jpg', 'group_chat/no_result_to_find.png', 'group_chat/no_result_to_find_low.jpg',
                                             'group_chat/no_result_to_find_low1.jpg'], True)
                    if no_result:
                        self.need_refresh = True
                        self.close_handle(search_tab_wnd)
                        raise Exception(f"can't find user {target} to chat")
                    if time.time() - user_search_start_time > 20:
                        raise Exception(f"find user {target} to chat timeout")
                    time.sleep(1)

            self.connect(self.handle)
            # Send messages
            self._send_messages(target, messages, file_paths)
            if search_tab_wnd is not None:
                self.close_handle(search_tab_wnd)
            self.last_send = target

            # Throw if there is any error popup windows
            if not self._check_error_popup():
                raise Exception("Failed to send messages")
        except Exception as e:
            if search_tab_wnd is not None:
                self.close_handle(search_tab_wnd)
            self.last_send = ''
            raise e

    @AirApp.print_run_time
    def send_group_messages(self, data):
        logging.debug('Send group messages, handle: %d, data: %s', self.handle, data)

        # Validate data before sending messages
        target, messages, file_paths = self._check_messages(data)

        self.connect(self.handle, foreground=True)

        # Popup userinfo menu
        self._popup_userinfo()

        # Open message manager
        pos = self.exists('userinfo_popup/message_manager_button.png')
        if not pos:
             raise Exception("Failed to find message manager button")
        self.click(pos, 1)

        message_manager_handle = find_window(class_name='MsgManagerWindow')
        if not message_manager_handle:
            raise Exception("Failed to open message manager window")
        self.connect(message_manager_handle)

        # Click dropdown button and search group
        self.click('message_manager_dropdown_btn.png', 0.5)
        self.click('message_manager_group_name_input.png', 0.5)
        self.copy(target)
        self.key("^v", 0.5)
        self.key("{ENTER}", 0.5)

        # Throw if the group cannot be found
        if self.exists('message_manager_no_result.png'):
            raise Exception('Group cannot be found')

        # Back to the main window
        self.double_click('message_manager_group_icon.png', 1)
        self.connect(self.handle)
        self.wait(0.5)

        # Send messages
        self._send_messages(target, messages, file_paths)

        # Close message manager
        self.close_handle(message_manager_handle)

        # Throw if there is any error popup windows
        if not self._check_error_popup():
            raise Exception("Failed to send messages")

    @AirApp.print_run_time
    def add_contacts(self, data):
        logging.debug('Add contacts, handle: %d, data: %s', self.handle, data)

        # Validate data before adding contacts
        contacts = self._check_contacts(data)

        self.connect(self.handle)

        # Click contact button
        self.click(['main/navbar_contact_button.png', 'main/navbar_contact_button_hover.png',
                    'main/navbar_contact_button_focus.png'])

        # Click new friend logo icon
        self.click('contact_new_friend_logo_btn.png', 0.5)
        for contact in contacts:
            target, reason = contact.get('target'), contact.get('reason')
            self.connect(self.handle)

            # Click new friend plus button
            self.click('main/contact_new_friend_button.png', 0.5)
            self.click('main/contact_new_friend_add_button.png', 0.5)
            user_search_handle = find_window(class_name='SearchExternalsWnd')
            if not user_search_handle:
                raise Exception("Failed to open search window")
            self.connect(user_search_handle)
            self.click(['contact_new_friend_search_input1.png', 'contact_new_friend_search_input2.png'], 0.5)
            self.copy(target)
            self.key("^v", 0.5)
            self.key("{ENTER}", 2)

            # Throw if the user cannot be found
            if not self.click('contact_new_friend_add_btn.png', 2):
                raise Exception('User cannot be found')

            # Connect to reason input window
            reason_input_handle = find_window(class_name='InputReasonWnd')
            if not reason_input_handle:
                raise Exception("Failed to open reason input window")
            self.connect(reason_input_handle)

            # Input reason if it exists
            if reason:
                close_pos = self.click('contact_new_friend_close_btn.png', 0.5)
                if close_pos:
                    self.click(close_pos, 0.5)
                    self.click((close_pos[0] - 50, close_pos[1]), 0.5)
                    self.copy(reason)
                    self.key("^v", 0.5)

            # Click apply button
            self.click('contact_new_friend_apply_btn.png', 0.5)
            try:
                self.close_handle(reason_input_handle)
            except:
                logging.error("Failed to close reason input window")
            try:
                self.close_handle(user_search_handle)
            except:
                logging.error("Failed to close user search window")

    @AirApp.print_run_time
    def create_group_chat(self, data):
        logging.debug('Create group chat, handle: %d, data: %s', self.handle, data)
        groupchatname, groupchatmembers = self._check_group_chat(data)

        self.connect(self.handle)

        # 新建群聊
        self.click('group_chat/group_chat_create_btn.png', 1)

        member_search_handle = find_window(class_name='weWorkSelectUser')
        if not member_search_handle:
            raise Exception("Failed to open search window")
        self.connect(member_search_handle)

        search_pos = self.exists('group_chat/group_member_search.png')
        self.click((search_pos[0], int(search_pos[1] - 30 * self.get_screen_scale_ratio())), 0.1)

        for member in groupchatmembers:
            self.copy(member)
            self.key("^a", 0.1)
            self.key("^v", 0.5)
            self.key("{ENTER}", 0.5)

        self.click('group_chat/create_group_chat.png', 5)

        self.connect(self.handle)
        group_setting_pos = self.exists('group_chat/group_setting.png')
        self.click((int(group_setting_pos[0] - 20 * self.get_screen_scale_ratio()), group_setting_pos[1]), 0.1)

        self.click('group_chat/group_name.png', 0.5)

        self.copy(groupchatname)
        self.key("^v", 0.1)
        self.key("{ENTER}", 2)

    @AirApp.print_run_time
    def edit_group_chat_name(self, data):
        logging.debug('edit group chat, handle: %d, data: %s', self.handle, data)
        groupchatname, newgroupchatname = self._check_edit_group_chat(data)
        if groupchatname == newgroupchatname:
            return

        self.connect(self.handle)
        # 点击搜索框
        search_input_pos = self.exists(['main/search_input.png', 'main/search_input_focus.png'])
        if not search_input_pos:
            self.need_refresh = True
            raise Exception("Failed to find search input")
        self.click((int(search_input_pos[0] + 120 * self.get_screen_scale_ratio()), search_input_pos), 0.5)

        # 点击全局搜索
        self.click('search_all.png', 0.5)
        search_tab_wnd = find_window(class_name_re='SearchTabWnd')
        if not search_tab_wnd:
            self.need_refresh = True
            raise Exception("Failed to open search window")
        # 连接全局搜索
        self.connect(search_tab_wnd)
        self.click('group_chat/group_chat.png')

        # 点击搜索
        self.click('group_chat/group_chat_search.png')
        self.copy(groupchatname)
        self.key("^a", 0.1)
        self.key("^v", 0.5)
        self.key("{ENTER}", 0.5)
        no_result = self.exists('group_chat/no_result_to_find.png', True)
        if no_result:
            self.close_handle(search_tab_wnd)
            raise Exception("can't find group chat")
        self.close_handle(search_tab_wnd)

        self.connect(self.handle)
        group_setting_pos = self.exists('group_chat/group_setting.png')
        self.click((int(group_setting_pos[0] - 20 * self.get_screen_scale_ratio()), group_setting_pos[1]), 0.1)

        self.click('group_chat/group_name.png', 0.5)

        self.copy(newgroupchatname)
        self.key("^a", 0.1)
        self.key("^v", 0.1)
        self.key("{ENTER}", 2)

    @AirApp.print_run_time
    def invite_group_chat(self, data):
        logging.debug('invite group chat, handle: %d, data: %s', self.handle, data)
        groupchatname, groupchatmembers = self._check_invite_group_chat(data)

        self.connect(self.handle)
        # 点击搜索框
        search_input_pos = self.exists(['main/search_input.png', 'main/search_input_focus.png'])
        if not search_input_pos:
            self.need_refresh = True
            raise Exception("Failed to find search input")
        self.click((int(search_input_pos[0] + 120 * self.get_screen_scale_ratio()), search_input_pos[1]), 0.5)

        # 点击全局搜索
        self.click('search_all.png', 0.5)
        search_tab_wnd = find_window(class_name_re='SearchTabWnd')
        if not search_tab_wnd:
            self.need_refresh = True
            raise Exception("Failed to open search window")
        # 连接全局搜索
        self.connect(search_tab_wnd)
        self.click('group_chat/group_chat.png')

        # 点击搜索
        self.click('group_chat/group_chat_search.png')
        self.copy(groupchatname)
        self.key("^v", 0.5)
        self.key("{ENTER}", 0.5)
        no_result = self.exists('group_chat/no_result_to_find.png', True)
        if no_result:
            self.close_handle(search_tab_wnd)
            raise Exception("can't find group chat")
        self.close_handle(search_tab_wnd)

        self.connect(self.handle)
        group_setting_pos = self.exists('group_chat/group_setting.png')
        self.click((int(group_setting_pos[0] + 20 * self.get_screen_scale_ratio()), group_setting_pos[1]), 0.1)

        select_user_wnd = find_window(class_name='weWorkSelectUser')
        if not select_user_wnd:
            self.need_refresh = True
            raise Exception("Failed to open search window")
        self.connect(select_user_wnd)

        # 点击搜索
        self.click('group_chat/group_chat_search.png')
        for member in groupchatmembers:
            self.copy(member)
            self.key("^a", 0.5)
            self.key("^v", 0.5)
            self.key("{ENTER}", 0.5)

        self.click('group_chat/invite_group_chat.png', 10)
        select_user_wnd = find_window(class_name='weWorkSelectUser')
        if not select_user_wnd:
            logging.debug('no new member to invite: %s, data: %s', groupchatname, groupchatmembers)
            self.close_handle(select_user_wnd)

    @AirApp.print_run_time
    def click_send_msg(self):
        send_msg_pos = self.exists(['send_msg.png'])
        if send_msg_pos is not None:
            interception.click(send_msg_pos[0], send_msg_pos[1])
            time.sleep(0.2)

    def send_text(self, text):
        self.copy(text)
        with interception.hold_key("ctrl"):
            interception.press("v")
        time.sleep(0.5)
        interception.press("enter")
        time.sleep(0.5)
        self.check_pop_up_and_close()
        interception.press("enter")

    @AirApp.print_run_time
    def _send_messages(self, target, messages, file_paths):
        """
        Sends messages to a target.

        Parameters:
        - target (str): The target where the messages will be sent.
        - messages (list): The list of messages to be sent. Each message should be a dictionary with 'type' and 'content' keys.
        - file_paths (dict): A dictionary mapping content values to their corresponding file paths.

        Returns:
        - None
        """
        self.connect(self.handle)
        self.wait(0.5)
        chat_message_input_pos = self.exists(
            ['main/chat_message_input1080.jpg', 'main/chat_message_input.png', 'main/chat_message_input_low.jpg'])
        if not chat_message_input_pos:
            raise Exception("Unable to get chat_message_input button")
        click_x = int(chat_message_input_pos[0])
        click_y = int(chat_message_input_pos[1] + 120 * self.get_screen_scale_ratio())
        interception.click(click_x, click_y)
        time.sleep(0.5)

        with interception.hold_key("ctrl"):
            interception.press("a")
        interception.press("backspace")

        file_count = 0
        for message in messages:
            type = message.get('type')
            content = message.get('content')
            if file_count == 20:
                interception.press("enter")
                time.sleep(1)
                file_count = 0
            if type == MessageType.TEXT:
                if len(content) <= 4000:
                    self.send_text(content)
                else:
                    texts = self._slice_text(content, 4000)
                    for v in texts:
                        self.send_text(v)
                        time.sleep(5)
            elif type == MessageType.IMAGE:
                set_clipboard_data_image(file_paths[content])
                with interception.hold_key("ctrl"):
                    interception.press("v")
                time.sleep(0.5)
                interception.press("enter")
                time.sleep(0.5)
                file_count = file_count + 1
            elif type == MessageType.VIDEO or type == MessageType.FILE:
                set_clipboard_data_file(file_paths[content])
                with interception.hold_key("ctrl"):
                    interception.press("v")
                time.sleep(0.5)
                interception.press("enter")
                time.sleep(0.5)
                if message.get('fileType') == 'xlsx' or message.get('fileType') == 'xls':
                    try:
                        online_file_window = find_window(class_name="WDialog", process=self.process)
                        if online_file_window is not None:
                            self.connect(online_file_window)
                            online_file_button = self.exists(['online_file_use_origin_file.png'], True)
                            if online_file_button is not None:
                                interception.click(online_file_button[0], online_file_button[1])
                                time.sleep(0.5)
                    except WindowNotFoundError as e:
                        logging.debug("无需处理")
                    finally:
                        try:
                            online_file_window = find_window(class_name="WDialog", process=self.process)
                            if online_file_window is not None:
                                self.close_handle(online_file_window)
                        except WindowNotFoundError as e:
                            logging.debug("无需处理")
                file_count = file_count + 1
            elif type == MessageType.CARD:
                card_json = json.loads(content)
                card_title = card_json.get('title')
                card_content = card_json.get('description')
                card_url = card_json.get('linkUrl')
                card_img_url = card_json.get('imgUrl')
                set_clipboard_data_card(card_title=card_title, card_content=card_content, card_url=card_url,
                                        card_img_url=card_img_url)
                with interception.hold_key("ctrl"):
                    interception.press("v")
                time.sleep(0.5)
                interception.press("enter")
                time.sleep(0.5)
            elif type == MessageType.MINIPROGRAM:
                miniprogram_json = json.loads(content)
                set_clipboard_data_miniprogram(miniprogram_json)
                with interception.hold_key("ctrl"):
                    interception.press("v")
                time.sleep(0.5)
                interception.press("enter")
                time.sleep(0.5)
            elif type == MessageType.MENTION:
                self.copy('@' + content)
                with interception.hold_key("ctrl"):
                    interception.press("v")
                time.sleep(0.5)
                interception.press("enter")
                time.sleep(0.5)
        interception.press("enter")
        time.sleep(0.5)

    @AirApp.print_run_time
    def _popup_userinfo(self):
        """
        Popup and display the userinfo menu.
        """
        # Find the navbar message button
        message_button_pos = self.exists(
            ['main/navbar_message_button1080.jpg', 'main/navbar_message_button.png',
             'main/navbar_message_button_focus.png',
             'main/navbar_message_button_hover.png'])
        if not message_button_pos:
            raise Exception("Unable to find message_button button")
        # Locate the avatar through the position of the navbar message button
        click_x = message_button_pos[0]
        click_y = int(message_button_pos[1] - 130 * self.get_screen_scale_ratio())
        interception.click(click_x, click_y, delay=1)
        time.sleep(1)

        # Connect to menu window
        menu_handle = find_window(class_name="WXworkWindow", process=self.process)
        if not menu_handle:
            return False
        self.connect(menu_handle, foreground=False)
        return True

    def _check_messages(self, data):
        """
        Checks the validity of the given data and downloads all files in the messages.
        """
        if not data or not data.get('target') or not data.get('messages'):
            raise Exception("Invalid data")
        target = data['target']
        messages = data['messages']
        for m in messages:
            if 'type' not in m or 'content' not in m:
                raise Exception("Invalid messages")
        dir_path = "{}\\{}\\{}\\{}".format(self.temps_path, self.app_id(), datetime.now().strftime('%Y%m%d'),
                                            datetime.now().strftime('%Y%m%d%H%M%S'))
        file_types = [MessageType.IMAGE, MessageType.VIDEO, MessageType.FILE]
        file_urls = [
            {message['content']: message['fileName']} for message in messages if
            message['type'] in file_types and message.get("fileName")
        ]
        file_paths = self.download(file_urls, dir_path)
        return target, messages, file_paths

    def _check_contacts(self, data):
        """
        Checks if the given data contains valid contacts.
        """
        if not data or not data.get('contacts'):
            raise Exception("Invalid data")
        contacts = data['contacts']
        for c in contacts:
            if 'target' not in c:
                raise Exception("Invalid contacts")
        return contacts

    def _check_group_chat(self, data):
        if not data or not data.get('groupChatName') or not data.get('groupChatMembers'):
            raise Exception("Invalid data")
        groupchatname = data['groupChatName']
        groupchatmembers = data['groupChatMembers']
        return groupchatname, groupchatmembers

    def _check_edit_group_chat(self, data):
        if not data or not data.get('groupChatName') or not data.get('newGroupChatName'):
            raise Exception("Invalid data")
        groupchatname = data['groupChatName']
        newgroupchatname = data['newGroupChatName']
        return groupchatname, newgroupchatname

    def _check_invite_group_chat(self, data):
        if not data or not data.get('groupChatName') or not data.get('groupChatMembers'):
            raise Exception("Invalid data")
        groupchatname = data['groupChatName']
        groupchatmembers = data['groupChatMembers']
        return groupchatname, groupchatmembers

    def _check_error_popup(self):
        """
        Check for any error popups.
        """
        message_box_elements = find_elements(class_name='WeWorkMessageBoxFrame')
        if message_box_elements:
            for element in message_box_elements:
                self.close_handle(element.handle)
            return False
        return True

    def _slice_text(self, obj, sec):
        """
         Slices the given text or string into sections of specified length.
        """
        return [obj[i:i + sec] for i in range(0, len(obj), sec)]
