server:
  # RPA server host.
#  host: ***********
  host: dmsc.clic
  # RPA server port.
  port: 443
  # RPA server path.
  path: /DMSC/rpa-server/rpa
  # RPA server ssl.
  ssl: True
  # sm4 encryption key.
  key: R7sD9pL2wT4qA5fG
  # sm4 encryption enable
  encryption_enable: True
  # RPA server authentication (weakness)
  auth: True
app:
  # Maximum number of clients to allow in the pool.
  size: 1
  # Custom app paths.
  path:
    dingtalk:
    lark:
    qq:
    tim:
    wechat:
    wecom: D:\WXWork\WXWork.exe
airtest:
  # See Airtest Docs: https://airtest.doc.io.netease.com/
  cvstrategy: [ mstpl,tpl,sift,brisk,surf ]
  timeout: 5
  timeout-tmp: 0.6
logging:
  # Logging level of root logger. (CRITICAL, ERROR, WARNING, INFO, DEBUG)
  level: INFO
  # Logging format.
  format: '%(asctime)s %(levelname)-5s [%(process)d-%(thread)d-%(threadName)s] %(module)s#%(funcName)s : %(message)s'
  # Specified a file and use it as the stream for logging.
  filename: ./logs/rpa-client.log

rpaversion: 1.2.1