package io.leego.rpa.service;

import io.leego.rpa.pojo.dto.*;
import io.leego.rpa.pojo.vo.AppVO;
import io.leego.rpa.pojo.vo.ClientVO;
import io.leego.rpa.pojo.vo.TaskVO;
import io.leego.rpa.pojo.vo.UserVO;
import io.leego.rpa.util.Option;
import io.leego.rpa.util.Page;
import io.leego.rpa.util.Result;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Yih
 */
public interface RpaService {

    Result<AppVO> getApp(String id);

    Result<Page<AppVO>> listApps(AppQueryDTO dto);

    Result<List<AppVO>> saveApps(AppSaveDTO dto);

    Result<UserVO> getUser(String id);

    Result<Page<UserVO>> listUsers(UserQueryDTO dto);

    Result<List<UserVO>> saveUsers(UserSaveDTO dto);

    Result<Void> deleteUser(UserDTO dto);

    Result<TaskVO> getTask(String id);

    Result<Page<TaskVO>> listTasks(TaskQueryDTO dto);

    Result<List<TaskVO>> saveTasks(TaskSaveDTO dto);

    Result<List<TaskVO>> deleteTasks(TaskDeleteDTO dto);

    Result<Page<ClientVO>> listClients(ClientQueryDTO dto);

    Result<Map<String, List<Option<String, String>>>> listTaskTypes();

    Result<Map<String, List<Option<Object, Object>>>> listConstants();

    /**
     * dms-aggr-chat
     * @param ids
     * @return
     */
    Result<List<TaskVO>> getTasksByIds(List<String> ids);

    /**
     * dms-aggr-chat saveTasks
     * @param param
     * @return
     */
    Result<List<TaskVO>> saveDMSTasks(String param);

    /**
     * 读取服务端本地文件，以文件流形式传出给客户端
     * @param url 服务端本地文件地址
     * @return
     */
    byte[] download(String url);

}
