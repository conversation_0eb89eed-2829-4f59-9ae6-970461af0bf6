package io.leego.rpa.controller;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import io.leego.rpa.pojo.dto.*;
import io.leego.rpa.pojo.vo.AppVO;
import io.leego.rpa.pojo.vo.ClientVO;
import io.leego.rpa.pojo.vo.TaskVO;
import io.leego.rpa.pojo.vo.UserVO;
import io.leego.rpa.service.RpaService;
import io.leego.rpa.util.AESUtil;
import io.leego.rpa.util.Option;
import io.leego.rpa.util.Page;
import io.leego.rpa.util.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
public class RpaController {
    private final RpaService rpaService;

    public RpaController(RpaService rpaService) {
        this.rpaService = rpaService;
    }

//    @GetMapping("apps/{id}")
//    public Result<AppVO> getApp(@PathVariable String id) {
//        return rpaService.getApp(id);
//    }

//    @GetMapping("apps")
//    public Result<Page<AppVO>> listApps(@Validated AppQueryDTO dto) {
//        return rpaService.listApps(dto);
//    }
//
//    @PostMapping("apps")
//    public Result<List<AppVO>> saveApps(@Validated @RequestBody AppSaveDTO dto) {
//        return rpaService.saveApps(dto);
//    }

//    @GetMapping("users/{id}")
//    public Result<UserVO> getUser(@PathVariable String id) {
//        return rpaService.getUser(id);
//    }

//    @GetMapping("users")
//    public Result<Page<UserVO>> listUsers(@Validated UserQueryDTO dto) {
//        return rpaService.listUsers(dto);
//    }

    @PostMapping("users")
    public Result<List<UserVO>> saveUsers(@Validated @RequestBody UserSaveDTO dto) {
        return rpaService.saveUsers(dto);
    }

    @PostMapping("deleteUser")
    public Result<Void> deleteUser(@Validated @RequestBody UserDTO dto) {
        return rpaService.deleteUser(dto);
    }

    @GetMapping("tasks/{id}")
    public Result<TaskVO> getTask(@PathVariable String id) {
        return rpaService.getTask(id);
    }

    @GetMapping("tasks")
    public Result<Page<TaskVO>> listTasks(@Validated TaskQueryDTO dto) {
        return rpaService.listTasks(dto);
    }

//    @PostMapping("tasks")
//    public Result<List<TaskVO>> saveTasks(@Validated @RequestBody TaskSaveDTO dto) {
//        return rpaService.saveTasks(dto);
//    }

//    @DeleteMapping("tasks")
//    public Result<List<TaskVO>> deleteTasks(@Validated @RequestBody TaskDeleteDTO dto) {
//        return rpaService.deleteTasks(dto);
//    }

    @GetMapping("clients")
    public Result<Page<ClientVO>> listClients(@Validated ClientQueryDTO dto) {
        return rpaService.listClients(dto);
    }

//    @GetMapping("tasks/types")
//    public Result<Map<String, List<Option<String, String>>>> listTaskTypes() {
//        return rpaService.listTaskTypes();
//    }

//    @GetMapping("constants")
//    public Result<Map<String, List<Option<Object, Object>>>> listConstants() {
//        return rpaService.listConstants();
//    }

    /**
     * dms-aggr-chat调用, 获取任务详情
     * @param ids
     * @return
     */
    @PostMapping("tasks")
    public Result<List<TaskVO>> getTasksByIds(@RequestBody String ids) {
        String key = "E13F3D92FC0E09EB2003C4069BF2A778";
        String decrypt = AESUtil.decrypt(ids, key);
        Type type = new TypeToken<List<String>>(){}.getType();
        List<String> idList = new Gson().fromJson(decrypt, type);
        return rpaService.getTasksByIds(idList);
    }

    /**
     * dms-aggr-chat调用，写入任务
     * @param dto
     * @return
     */
    @PostMapping("saveTasks")
    public Result<List<TaskVO>> saveTasks(@RequestBody String dto) {
        return rpaService.saveDMSTasks(dto);
    }

    /**
     * rpa-client下载
     * @param url
     * @return
     */
    @GetMapping("download")
    public byte[] download(@RequestParam("url") String url) {
        return rpaService.download(url);
    }

}
