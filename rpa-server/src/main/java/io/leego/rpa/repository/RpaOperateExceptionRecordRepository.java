package io.leego.rpa.repository;

import io.leego.rpa.entity.RpaOperateExceptionRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.Nullable;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface RpaOperateExceptionRecordRepository extends JpaRepository<RpaOperateExceptionRecord, String>, QuerydslRepository<RpaOperateExceptionRecord> {



}
