package io.leego.rpa.server.codec;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.leego.rpa.util.Message;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageDecoder;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;

/**
 * <AUTHOR> <PERSON>h
 */
@Component
@ChannelHandler.Sharable
public class WebSocketDecoder extends MessageToMessageDecoder<TextWebSocketFrame> {
    private static final Logger logger = LoggerFactory.getLogger(WebSocketDecoder.class);
    private final ObjectMapper objectMapper;

    @Value("${rpa.sm4-encryption.enable}")
    private boolean sm4_encoder_enable;

    @Value("${rpa.sm4-encryption.key}")
    private String sm4_encryption_key;

    public WebSocketDecoder(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    protected void decode(ChannelHandlerContext context, TextWebSocketFrame msg, List<Object> out) {
        try {
            if (sm4_encoder_enable){
                logger.debug("req websocket message: " + msg.text());
                // key必须是16位
                SymmetricCrypto sm4 = SmUtil.sm4(sm4_encryption_key.getBytes());
                String decryptStr = sm4.decryptStr(msg.text(), CharsetUtil.CHARSET_UTF_8);
                logger.debug("req decoded websocket message: " + decryptStr);
                out.add(objectMapper.readValue(decryptStr, Message.class));
            }else {
                out.add(objectMapper.readValue(msg.text(), Message.class));
            }

        } catch (IOException e) {
            logger.error("Invalid message: {}", msg, e);
            context.close();
        } catch (Exception e) { // 捕获所有异常
            logger.error("Error processing message: {}", msg, e);
            // 出现异常时关闭连接
            context.close();
        }

    }
}
