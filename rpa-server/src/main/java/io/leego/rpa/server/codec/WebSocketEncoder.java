package io.leego.rpa.server.codec;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.leego.rpa.util.Message;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageEncoder;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> Yih
 */
@Component
@ChannelHandler.Sharable
public class WebSocketEncoder extends MessageToMessageEncoder<Message> {
    private static final Logger logger = LoggerFactory.getLogger(WebSocketEncoder.class);
    private final ObjectMapper objectMapper;
    @Value("${rpa.sm4-encryption.enable}")
    private boolean sm4_encoder_enable;
    @Value("${rpa.sm4-encryption.key}")
    private String sm4_encryption_key;
    public WebSocketEncoder(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    protected void encode(ChannelHandlerContext context, Message msg, List<Object> out) {
        try {
            String sendMsg = objectMapper.writeValueAsString(msg);
            if (sm4_encoder_enable){
                logger.debug("resp websocket message: " + sendMsg);
                // key必须是16位
                SymmetricCrypto sm4 = SmUtil.sm4(sm4_encryption_key.getBytes());
                String EncryptBase64cryptStr = sm4.encryptBase64(sendMsg, CharsetUtil.CHARSET_UTF_8);
                logger.debug("resp encoded websocket message: " + EncryptBase64cryptStr);
                out.add(new TextWebSocketFrame(EncryptBase64cryptStr));
            } else {
                out.add(new TextWebSocketFrame(sendMsg));
            }
        } catch (IOException e) {
            logger.error("Invalid message: {}", msg, e);
        }
    }
}
