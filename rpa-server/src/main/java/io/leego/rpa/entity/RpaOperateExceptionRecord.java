package io.leego.rpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.LocalDateTime;

/**
 * Entity class representing the rpa_operate_exception_record table.
 *
 * <AUTHOR> lyc
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Entity
@Table(name = "rpa_operate_exception_record")
public class RpaOperateExceptionRecord {
    @Id
    @Column(nullable = false, length = 32)
    private String id;

    @Column(name = "org_code", nullable = false, length = 8)
    private String orgCode;

    @Column(name = "group_code", nullable = false, length = 32)
    private String groupCode;

    @Column(name = "app_id", nullable = false, length = 64)
    private String appId;

    @Column(name = "user_id", nullable = false, length = 64)
    private String userId;

    @Column(name = "biz_date", nullable = false, length = 8)
    private String bizDate;

    @Column(name = "type", nullable = false, length = 32)
    private String type;

    @Column(name = "\"desc\"", length = 4096)
    private String desc;

    @Column(name = "gmt_created", nullable = false)
    private LocalDateTime gmtCreated;

    @Column(name = "creator", nullable = false, length = 16)
    private String creator;
}
