package io.leego.rpa.constant;

/**
 * <AUTHOR>
 */
public final class Messages {
    /** Common */
    public static final String PAGE_INVALID = "page.invalid";
    public static final String SORT_INVALID = "sort.invalid";
    public static final String PARAM_INVALID = "param.invalid";
    /** App */
    public static final String APP_ABSENT = "app.absent";
    public static final String APP_PRESENT = "app.present";
    public static final String APP_DISABLED = "app.disabled";
    public static final String APP_INVALID = "app.invalid";
    public static final String APP_ID_INVALID = "app.id.invalid";
    public static final String APP_NAME_INVALID = "app.name.invalid";
    /** User */
    public static final String USER_ABSENT = "user.absent";
    public static final String USER_PRESENT = "user.present";
    public static final String USER_DISABLED = "user.disabled";
    public static final String USER_INVALID = "user.invalid";
    public static final String USER_ACCOUNT_INVALID = "user.account.invalid";
    public static final String USER_APP_ID_INVALID = "user.app.id.invalid";
    /** Task */
    public static final String TASK_ABSENT = "task.absent";
    public static final String TASK_PRESENT = "task.present";
    public static final String TASK_INVALID = "task.invalid";
    public static final String TASK_ID_INVALID = "task.id.invalid";
    public static final String TASK_TYPE_INVALID = "task.type.invalid";
    public static final String TASK_STATUS_INVALID = "task.status.invalid";
    public static final String TASK_USER_ID_INVALID = "task.user.id.invalid";
    public static final String TASK_CANCELLED_APP_UNSUPPORTED = "task.cancelled.app.unsupported";
    public static final String TASK_CANCELLED_APP_UNAVAILABLE = "task.cancelled.app.unavailable";
    public static final String TASK_CANCELLED_USER_UNSUPPORTED = "task.cancelled.user.unsupported";
    public static final String TASK_CANCELLED_USER_UNAVAILABLE = "task.cancelled.user.unavailable";
    public static final String TASK_CANCELLED_USER_LOGGED = "task.cancelled.user.logged";

    private Messages() {
    }
}
