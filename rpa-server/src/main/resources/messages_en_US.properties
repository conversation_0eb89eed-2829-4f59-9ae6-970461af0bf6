# Common
page.invalid=Invalid paging parameter
sort.invalid=Invalid sorting property
param.invalid=Invalid parameter
# App
app.absent=The app cannot be found
app.present=The app already exists
app.disabled=The app has been disabled
app.invalid=Invalid app
app.id.invalid=Invalid id
app.name.invalid=Invalid name
# User
user.absent=The user cannot be found
user.present=The user already exists
user.disabled=The user has been disabled
user.invalid=Invalid user
user.account.invalid=Invalid account
user.app.id.invalid=Invalid app id
# Task
task.absent=The task cannot be found
task.present=The task already exists
task.invalid=Invalid task
task.id.invalid=Invalid id
task.type.invalid=Invalid type
task.status.invalid=Invalid status
task.user.id.invalid=Invalid user id
task.cancelled.app.unsupported=Unsupported app
task.cancelled.app.unavailable=Unavailable app
task.cancelled.user.unsupported=Unsupported user
task.cancelled.user.unavailable=Unavailable user
task.cancelled.user.logged=The user is logged in or is logging in