# port
server.port=80
# name
spring.application.name=rpa-server
# logging
logging.file.name=./logs/${spring.application.name}.log
logging.level.io.leego.rpa=debug
logging.level.org.hibernate.SQL=debug
logging.logback.rollingpolicy.clean-history-on-start=false
logging.logback.rollingpolicy.max-file-size=500MB
logging.logback.rollingpolicy.total-size-cap=10GB
logging.logback.rollingpolicy.max-history=30
# actuator
#management.endpoints.web.exposure.include=*
#management.endpoint.health.show-details=WHEN_AUTHORIZED
# Disable Actuator
management.endpoints.enabled-by-default=false
management.server.port=-1
# Disable Web Resources
spring.web.resources.add-mappings=false
# messages
spring.messages.basename=messages,constants
spring.messages.use-code-as-default-message=true
# datasource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=******************************************************
spring.datasource.username=root
spring.datasource.password=
# redis
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.database=0
# rpa-server-ip
rpa.server.addr=http://localhost:80
# sm4 encoder enabled
rpa.sm4-encryption.enable=true
# sm4 encryption key
rpa.sm4-encryption.key=R7sD9pL2wT4qA5fG
# require auth
rpa.server.auth-required=true
# delete message data after task executed
rpa.server.delete-after-executed=true
# encrypt tasks
rpa.server.encrypt-task=true
# encrypt tasks key
rpa.server.encrypt-task-key=7gH5k1TzP3jW8xY9
