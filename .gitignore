/rpa-client/temps/
/rpa-client/dist/
/rpa-client/logs/


.idea/
.DS_Store
node_modules/
package-lock.json
yarn.lock
.vscode/
.history/
logs/
target/
pid
# Python cache files
**/__pycache__/
**/*.py[cod]
**/*$py.class
**/.pytest_cache/
**/.coverage
**/htmlcov/

# Compiled class file
*.class
# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*
replay_pid*

__pycache__
.pyarmor
dump.rdb

# Python
__pycache__/
*.py[cod]
*$py.class
*.pyc

# 其他已有的忽略规则
/rpa-client/obfuscated/