@echo off
setlocal enabledelayedexpansion


REM ##################################################################
REM #                      CONFIGURATION                             #
REM ##################################################################
SET "DEBUG_MODE=true"
SET "REMOTE_EXE_FILENAME=WeComRPA-interception.exe"

REM # --- General Settings ---
SET "APP_NAME=WeComRPA"
SET "EXE_NAME=WeComRPA.exe"
SET "SHORTCUT_NAME=WeComRPA.lnk"
SET "INSTALL_DIR=D:\rpa"

REM # --- FTP & Download Settings ---
SET "FTP_HOST=***********"
SET "FTP_USER=ftpuser"
SET "FTP_PASS=ftppassword"
SET "FTP_REMOTE_DIR=rpa"
SET "LOCAL_DOWNLOAD_DIR=%TEMP%\rpa_update"

REM # --- Dynamic Variables (do not change) ---
SET "FTP_SCRIPT_PATH=%TEMP%\ftp_commands.txt"
SET "NEW_EXE_LOCAL_PATH=%LOCAL_DOWNLOAD_DIR%\%EXE_NAME%"

REM ##################################################################
REM #                 STEP 1: FTP DOWNLOAD & VERIFY                  #
REM ##################################################################

REM --- Cleanup and prepare download directory ---
if exist "%LOCAL_DOWNLOAD_DIR%" (
    if "%DEBUG_MODE%"=="true" echo      Cleaning up old download directory...
    rd /s /q "%LOCAL_DOWNLOAD_DIR%"
)
md "%LOCAL_DOWNLOAD_DIR%"

REM --- Download EXE file ---
(
    echo user %FTP_USER% %FTP_PASS%
    echo binary
    echo cd %FTP_REMOTE_DIR%
    echo get %REMOTE_EXE_FILENAME% "%NEW_EXE_LOCAL_PATH%"
    echo quit
) > "%FTP_SCRIPT_PATH%"

if "%DEBUG_MODE%"=="true" (
    echo.
    echo      --- FTP Script Content ---
    type "%FTP_SCRIPT_PATH%"
    echo      --------------------------
    echo      Executing FTP command...
    ftp -n -s:"%FTP_SCRIPT_PATH%" %FTP_HOST%
    echo      --------------------------
) else (
    ftp -n -s:"%FTP_SCRIPT_PATH%" %FTP_HOST% >nul 2>nul
)

if not exist "%NEW_EXE_LOCAL_PATH%" (
    echo [ERROR] Failed to download EXE file '%REMOTE_EXE_FILENAME%' from FTP.
    goto cleanup_and_fail
)

if "%DEBUG_MODE%"=="true" (
    echo      Successfully downloaded package.
)

REM ##################################################################
REM #              CORE UPGRADE LOGIC (Pure Batch)                     #
REM ##################################################################
if "%DEBUG_MODE%"=="true" echo [STEP 1/2] Executing core upgrade logic...

set "FULL_INSTALL_PATH=%INSTALL_DIR%\%EXE_NAME%"
set "BACKUP_EXE_PATH=%FULL_INSTALL_PATH%.old"

REM --- 1. Stop the running process ---
if "%DEBUG_MODE%"=="true" echo      Stopping process %EXE_NAME%...
taskkill /IM "%EXE_NAME%" /F >nul 2>nul
REM Using ping for a reliable delay in non-interactive environments instead of timeout.
ping 127.0.0.1 -n 3 >nul

REM --- 2. Backup the existing application ---
if "%DEBUG_MODE%"=="true" echo      Backing up existing application...

REM Clean up previous backup files to prevent errors
if exist "%BACKUP_EXE_PATH%" del "%BACKUP_EXE_PATH%"

REM Backup current files by renaming them
if exist "%FULL_INSTALL_PATH%" (
    move "%FULL_INSTALL_PATH%" "%BACKUP_EXE_PATH%" >nul
    if "%DEBUG_MODE%"=="true" echo      Backed up '%EXE_NAME%' to '%EXE_NAME%.old'
)

REM --- 3. Replace with the new version ---
if "%DEBUG_MODE%"=="true" echo      Deploying new version...
move "%NEW_EXE_LOCAL_PATH%" "%FULL_INSTALL_PATH%" >nul

REM --- 3.5 创建/更新桌面快捷方式 ---
if "%DEBUG_MODE%"=="true" echo      正在创建/更新桌面快捷方式...
set "shortcutPath=%PUBLIC%\Desktop\%SHORTCUT_NAME%"

if exist "%FULL_INSTALL_PATH%" (
    powershell.exe -ExecutionPolicy Bypass -Command "$ws = New-Object -ComObject WScript.Shell; $s = $ws.CreateShortcut('!shortcutPath!'); $s.TargetPath = '!FULL_INSTALL_PATH!'; $s.WorkingDirectory = '!INSTALL_DIR!'; $s.Save()" >nul 2>nul
    
    REM 给予文件系统一点时间响应
    ping 127.0.0.1 -n 2 >nul

    if exist "%shortcutPath%" (
        if "%DEBUG_MODE%"=="true" echo      成功在 '!shortcutPath!' 创建/更新快捷方式。
    ) else (
        if "%DEBUG_MODE%"=="true" echo      [警告] 创建快捷方式失败。可能是因为对公共桌面目录没有足够的写入权限。
    )
) else (
    if "%DEBUG_MODE%"=="true" echo      [警告] 在 '!FULL_INSTALL_PATH!' 未找到主程序，跳过创建快捷方式。
)

REM --- 4. Restart the application in the active user session ---
if "%DEBUG_MODE%"=="true" echo      Attempting to restart application in user session...
set "TASK_NAME=StartRPA_%RANDOM%"
REM Create a temporary scheduled task to run as the INTERACTIVE user, which targets the active desktop session.
schtasks /create /f /tn "%TASK_NAME%" /tr "'%FULL_INSTALL_PATH%'" /sc once /st 23:59 /ru "INTERACTIVE" >nul
if !errorlevel! equ 0 (
    schtasks /run /tn "%TASK_NAME%" >nul
    REM Give it a moment to start before we delete the task
    ping 127.0.0.1 -n 3 >nul
    schtasks /delete /f /tn "%TASK_NAME%" >nul
    if "%DEBUG_MODE%"=="true" echo      Successfully triggered start via scheduled task for INTERACTIVE user.
) else (
    if "%DEBUG_MODE%"=="true" echo      [WARNING] Failed to create scheduled task. Falling back to simple 'start'.
    start "" "%FULL_INSTALL_PATH%"
)

goto cleanup_and_succeed

:cleanup_and_succeed
REM ##################################################################
REM #                           CLEANUP                              #
REM ##################################################################
if "%DEBUG_MODE%"=="true" (
    echo [STEP 2/2] Cleaning up temporary files...
)
if exist "%FTP_SCRIPT_PATH%" del "%FTP_SCRIPT_PATH%"
if exist "%LOCAL_DOWNLOAD_DIR%" rd /s /q "%LOCAL_DOWNLOAD_DIR%"
if "%DEBUG_MODE%"=="true" (
    echo      Cleanup complete.
    echo.
)
echo [SUCCESS]
exit /b 0 
goto end

:cleanup_and_fail
if "%DEBUG_MODE%"=="true" (
    echo [STEP 2/2] Cleaning up temporary files...
)
if exist "%FTP_SCRIPT_PATH%" del "%FTP_SCRIPT_PATH%"
if exist "%LOCAL_DOWNLOAD_DIR%" rd /s /q "%LOCAL_DOWNLOAD_DIR%"
if "%DEBUG_MODE%"=="true" (
    echo      Cleanup complete.
)
echo [FAILURE]
exit /b 1
goto end

:end
endlocal 