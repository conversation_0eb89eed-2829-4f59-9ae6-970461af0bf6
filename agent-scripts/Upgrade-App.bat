@echo off
setlocal enabledelayedexpansion

REM ##################################################################
REM #                      CONFIGURATION                             #
REM ##################################################################
SET "DEBUG_MODE=true"
REM --- General Settings ---
SET "EXE_NAME=WeComRPA.exe"
SET "INSTALL_DIR=D:\rpa"

REM ##################################################################
REM #                           LOGIC                                #
REM ##################################################################

REM --- 1. Validate Arguments ---
SET "NEW_EXE_LOCAL_PATH=%TEMP%\rpa_update\WeComRPA.exe"

REM --- 2. Define Paths ---
set "FULL_INSTALL_PATH=%INSTALL_DIR%\%EXE_NAME%"
set "BACKUP_EXE_PATH=%FULL_INSTALL_PATH%.old"
set "VERSION_FILE=%INSTALL_DIR%\version.txt"
set "BACKUP_VERSION_PATH=%VERSION_FILE%.old"

if "%DEBUG_MODE%"=="true" echo [STEP 1/2] Executing core upgrade logic...

REM --- 3. Stop, Backup, Replace ---
if "%DEBUG_MODE%"=="true" echo      Stopping process %EXE_NAME%...
taskkill /IM "%EXE_NAME%" /F >nul 2>nul
timeout /t 2 /nobreak >nul

if "%DEBUG_MODE%"=="true" echo      Backing up existing application...
if exist "%BACKUP_EXE_PATH%" del "%BACKUP_EXE_PATH%" >nul 2>nul
if exist "%BACKUP_VERSION_PATH%" del "%BACKUP_VERSION_PATH%" >nul 2>nul
if exist "%FULL_INSTALL_PATH%" move "%FULL_INSTALL_PATH%" "%BACKUP_EXE_PATH%" >nul
if exist "%VERSION_FILE%" move "%VERSION_FILE%" "%BACKUP_VERSION_PATH%" >nul

if "%DEBUG_MODE%"=="true" echo      Deploying new version...
move "%NEW_EXE_LOCAL_PATH%" "%FULL_INSTALL_PATH%" >nul

REM --- 4. Restart Application ---
if "%DEBUG_MODE%"=="true" echo      Restarting application in user session...
set "TASK_NAME=StartRPA_%RANDOM%"
schtasks /create /f /tn "%TASK_NAME%" /tr "'%FULL_INSTALL_PATH%'" /sc once /st 23:59 /ru "INTERACTIVE" >nul
if !errorlevel! equ 0 (
    schtasks /run /tn "%TASK_NAME%" >nul
    ping 127.0.0.1 -n 3 >nul
    schtasks /delete /f /tn "%TASK_NAME%" >nul
) else (
    start "" "%FULL_INSTALL_PATH%"
)

if "%DEBUG_MODE%"=="true" echo [STEP 2/2] Upgrade finished.
echo [SUCCESS]
exit /b 0 