@echo off
setlocal enabledelayedexpansion

REM ##################################################################
REM #                      CONFIGURATION                             #
REM ##################################################################
SET "DEBUG_MODE=true"
REM --- FTP Settings ---
SET "FTP_HOST=***********"
SET "FTP_USER=ftpuser"
SET "FTP_PASS=ftppassword"
SET "FTP_REMOTE_DIR=rpa"
REM --- Local Settings ---
SET "LOCAL_DOWNLOAD_DIR=%TEMP%\rpa_update"
SET "EXE_NAME=WeComRPA.exe"

REM ##################################################################
REM #                          LOGIC                                 #
REM ##################################################################

REM --- 1. Validate Arguments ---
SET "REMOTE_EXE_FILENAME=WeComRPA-interception.exe"
SET "NEW_EXE_LOCAL_PATH=%LOCAL_DOWNLOAD_DIR%\%EXE_NAME%"

REM --- 2. Download from FTP ---
if "%DEBUG_MODE%"=="true" echo [STEP 1/2] Downloading '%REMOTE_EXE_FILENAME%'...

if exist "%LOCAL_DOWNLOAD_DIR%" rd /s /q "%LOCAL_DOWNLOAD_DIR%" >nul 2>nul
md "%LOCAL_DOWNLOAD_DIR%"

SET "FTP_SCRIPT_PATH=%TEMP%\ftp_commands.txt"
(
    echo user %FTP_USER% %FTP_PASS%
    echo binary
    echo cd %FTP_REMOTE_DIR%
    echo get "%REMOTE_EXE_FILENAME%" "%NEW_EXE_LOCAL_PATH%"
    echo quit
) > "%FTP_SCRIPT_PATH%"

ftp -n -s:"%FTP_SCRIPT_PATH%" %FTP_HOST% >nul 2>nul

del "%FTP_SCRIPT_PATH%" >nul 2>nul

REM --- 3. Verify and Output ---
if not exist "%NEW_EXE_LOCAL_PATH%" (
    if "%DEBUG_MODE%"=="true" echo [ERROR] Download failed. File not found locally.
    echo [FAILURE]
    exit /b 1
)

if "%DEBUG_MODE%"=="true" echo [STEP 2/2] Download successful.
echo %NEW_EXE_LOCAL_PATH%
exit /b 0 