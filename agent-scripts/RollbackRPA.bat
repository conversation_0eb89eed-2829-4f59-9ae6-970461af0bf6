@echo off
setlocal enabledelayedexpansion

REM ##################################################################
REM #                      CONFIGURATION                             #
REM ##################################################################
SET "DEBUG_MODE=false"

REM # --- General Settings ---
SET "EXE_NAME=WeComRPA.exe"
SET "INSTALL_DIR=D:\rpa"

REM # --- Dynamic Variables (do not change) ---
set "FULL_INSTALL_PATH=%INSTALL_DIR%\%EXE_NAME%"
set "BACKUP_EXE_PATH=%FULL_INSTALL_PATH%.old"

REM ##################################################################
REM #                    CORE ROLLBACK LOGIC                         #
REM ##################################################################
if "%DEBUG_MODE%"=="true" echo [STEP 1/2] Executing rollback logic...

if not exist "%BACKUP_EXE_PATH%" (
    if "%DEBUG_MODE%"=="true" echo [ERROR] No backup file found ('%BACKUP_EXE_PATH%'^). Cannot rollback.
    goto end_fail
)
if "%DEBUG_MODE%"=="true" echo      Backup file found. Proceeding with rollback.

if "%DEBUG_MODE%"=="true" echo      Stopping process %EXE_NAME%...
taskkill /IM "%EXE_NAME%" /F >nul 2>nul
ping 127.0.0.1 -n 2 >nul

if "%DEBUG_MODE%"=="true" echo      Restoring from backup...
if exist "%FULL_INSTALL_PATH%" del "%FULL_INSTALL_PATH%"
move "%BACKUP_EXE_PATH%" "%FULL_INSTALL_PATH%" >nul
if "%DEBUG_MODE%"=="true" echo      Rollback complete.

if "%DEBUG_MODE%"=="true" echo      Attempting to restart application in user session...
set "TASK_NAME=StartRPA_%RANDOM%"
schtasks /create /f /tn "%TASK_NAME%" /tr "'%FULL_INSTALL_PATH%'" /sc once /st 23:59 /ru "INTERACTIVE" >nul
if !errorlevel! equ 0 (
    schtasks /run /tn "%TASK_NAME%" >nul
    ping 127.0.0.1 -n 3 >nul
    schtasks /delete /f /tn "%TASK_NAME%" >nul
    if "%DEBUG_MODE%"=="true" echo      Successfully triggered start via scheduled task for INTERACTIVE user.
) else (
    if "%DEBUG_MODE%"=="true" echo      [WARNING] Failed to create scheduled task. Falling back to simple 'start'.
    start "" "%FULL_INSTALL_PATH%"
)

if "%DEBUG_MODE%"=="true" (
    echo [STEP 2/2] Rollback process finished.
)
echo [SUCCESS]
goto end

:end_fail
if "%DEBUG_MODE%"=="true" (
    echo [ERROR] Rollback process failed.
)
echo [FAILURE]

:end
endlocal 