@echo off

set "INSTALL_DIR=D:\rpa"
set "EXE_NAME=WeComRPA.exe"
set "FULL_PATH=%INSTALL_DIR%\%EXE_NAME%"

powershell -NoProfile -Command "try { $path = '%FULL_PATH%'; if (Test-Path -LiteralPath $path) { $ver = (Get-Item -LiteralPath $path -ErrorAction Stop).VersionInfo.ProductVersion; if ($ver) { Write-Host $ver.Trim() } else { Write-Host 'VERSION_NOT_FOUND' } } else { Write-Host 'UNKNOWN' } } catch { Write-Host 'VERSION_READ_ERROR' }" 